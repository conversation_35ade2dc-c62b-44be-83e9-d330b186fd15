package com.jinghang.cash.modules.project.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghang.cash.api.dto.GuaranteeConfigDto;
import com.jinghang.cash.modules.project.domain.GuaranteeConfig;
import com.jinghang.cash.modules.project.domain.dto.GuaranteeConfigQueryCriteria;
import com.jinghang.cash.utils.PageResult;

/**
* @description 服务接口
* <AUTHOR>
* @date 2025-08-22
**/
public interface GuaranteeConfigService extends IService<GuaranteeConfig> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param page 分页参数
    * @return PageResult
    */
//    PageInfo<GuaranteeConfig> queryAll(GuaranteeConfigQueryCriteria criteria, Page<Object> page);


    /**
     * 查询数据分页
     * @param criteria 条件
     * @param page 分页参数
     * @return PageResult
     */
    PageResult<GuaranteeConfig> selectAll(GuaranteeConfigQueryCriteria criteria, Page<Object> page);

    /**
     * 创建
     * @param id /
     */
    GuaranteeConfig selectByid(String id);

//    /**
//    * 查询所有数据不分页
//    * @param criteria 条件参数
//    * @return List<GuaranteeConfigDto>
//    */
//    List<GuaranteeConfig> queryAll(GuaranteeConfigQueryCriteria criteria);

    /**
    * 创建
    * @param resources /
    */
    void create(GuaranteeConfig resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(GuaranteeConfig resources);

    void enable(GuaranteeConfigDto resources);


}