package com.jinghang.cash.modules.project.rest;

import com.jinghang.cash.annotation.AnonymousAccess;
import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.project.domain.dto.ProjectInfoQueryCriteria;
import com.jinghang.cash.modules.project.service.ProjectInfoService;
import com.jinghang.cash.modules.project.service.domain.vo.ProjectInfoVo;
import com.jinghang.cash.utils.PageResult;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 项目信息管理
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 11:43
 */
@RestController
@RequestMapping("/projectInfo")
public class ProjectInfoController {

    @Autowired
    private ProjectInfoService projectInfoService;

    @PostMapping(value = "/query/{projectCode}")
    @ApiOperation("根据项目编码查询项目信息")
    @AnonymousAccess
    public RestResult<ProjectInfoDto> queryProjectInfo(@PathVariable String projectCode) {
        ProjectInfoDto projectInfo = projectInfoService.queryProjectInfo(projectCode);
        return RestResult.success(projectInfo);
    }

    @PostMapping(value = "/queryAll")
    @ApiOperation("查询所有生效项目信息")
    @AnonymousAccess
    public RestResult<List<ProjectInfoDto>> queryAllEnabledProjects() {
        List<ProjectInfoDto> projectInfoList = projectInfoService.queryAllEnabledProjects();
        return RestResult.success(projectInfoList);
    }


    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('projectInfo:list')")
    public void exportProjectInfo(HttpServletResponse response, ProjectInfoQueryCriteria criteria) throws IOException {
        projectInfoService.download(projectInfoService.queryAll(criteria), response);
    }

    @GetMapping("/page")
    @ApiOperation("查询project")
    @PreAuthorize("@el.check('projectInfo:queryAllPage')")
    public RestResult<PageResult<ProjectInfoVo>> queryAllPage(ProjectInfoQueryCriteria criteria){
        return RestResult.success(projectInfoService.queryAllPage(criteria));
    }

    @GetMapping("/info")
    @ApiOperation("查询project详情")
    @PreAuthorize("@el.check('projectInfo:queryProjectInfo')")
    public RestResult<ProjectInfoVo> getProjectInfo(@RequestParam String id){
        return RestResult.success(projectInfoService.getProjectInfo(id));
    }

    @PostMapping("/create")
    @ApiOperation("新增project")
    @PreAuthorize("@el.check('projectInfo:add')")
    public RestResult<Object> createProjectInfo(@Validated @RequestBody ProjectInfoDto resources){
        projectInfoService.create(resources);
        return RestResult.success();
    }

    @PostMapping("/enable")
    @ApiOperation("启用禁用")
    @PreAuthorize("@el.check('projectInfo:enable')")
    public RestResult<Object> enable(@Validated @RequestBody ProjectInfoDto resources){
        projectInfoService.enable(resources);
        return RestResult.success();
    }

    @PostMapping("/update")
    @ApiOperation("修改project")
    @PreAuthorize("@el.check('projectInfo:edit')")
    public RestResult<Object> updateProjectInfo(@Validated @RequestBody ProjectInfoDto resources){
        projectInfoService.update(resources);
        return RestResult.success();
    }

    @PostMapping("/del")
    @ApiOperation("删除project")
    @PreAuthorize("@el.check('projectInfo:del')")
    public RestResult<Object> deleteProjectInfo(@ApiParam(value = "传ID数组[]") @RequestBody List<String> ids) {
        projectInfoService.deleteAll(ids);
        return RestResult.success();
    }
}
