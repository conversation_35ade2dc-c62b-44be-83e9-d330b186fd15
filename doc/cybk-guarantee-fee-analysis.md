# 长银融担费计算分析报告

## 概述

分析长银资方定价利率变化对融担费计算的影响。当对资利率变小时，融担费应该增加，确保融担收益。

## 1. 当前融担费计算逻辑

### 1.1 两种利率体系

#### 1.1.1 Flow模块中的利率配置

**位置**: `src/main/java/com/maguo/loan/cash/flow/enums/QhBank.java`

```java
CYBK(new BigDecimal("0.239900"), new BigDecimal("0.1500"), BankChannel.CYBK, BindSignMode.SHARE, false, "长银消金", 5),
```

- `bankCustomRate`: **23.99%** - 银行对客利率
- `bankRate`: **15%** - 银行合同利率（对资利率）

#### 1.1.2 Rate服务中的利率体系

**位置**: `src/main/java/com/maguo/loan/cash/flow/enums/RateLevel.java`

```java
RATE_24(new BigDecimal("0.2399")),  // 23.99% - 权益客户
RATE_36(new BigDecimal("0.3599")),  // 35.99% - 普通客户
```

### 1.2 融担费计算公式

**核心公式**: `融担费 = 对客利率 - 对资利率`

## 2. 各模块融担费计算分析

### 2.1 Flow模块 - Rate36Service（试算结清融担费）

**位置**: `src/main/java/com/maguo/loan/cash/flow/service/rate/Rate36Service.java`

#### 2.1.1 当前计算逻辑
```java
public BigDecimal trialClearGuaranteeAmt(Loan loan, Integer period, List<RepayPlan> repayPlans, LocalDate date) {
    //融担费费率
    QhBank qhBank = QhBank.getQhBankBy(loan.getBankChannel());
    BigDecimal guaranteeRate = qhBank.getBankCustomRate().subtract(qhBank.getBankRate());
    // guaranteeRate = 23.99% - 15% = 8.99%
    
    // 计算融担费...
}
```

#### 2.1.2 问题分析
- **使用固定利率**: 使用QhBank枚举中的固定15%对资利率
- **影响**: 当对资利率变小时，这里不会自动调整

#### 2.1.3 改造方案
```java
public BigDecimal trialClearGuaranteeAmt(Loan loan, Integer period, List<RepayPlan> repayPlans, LocalDate date) {
    // 改为使用loan中的动态利率
    BigDecimal guaranteeRate = loan.getIrrRate().subtract(loan.getBankRate());
    // 或者从对应的Credit获取动态利率
    
    // 计算融担费...
}
```

### 2.2 Capital模块 - CYBKLoanService（担保费计算）

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/banks/cybk/service/CYBKLoanService.java`

#### 2.2.1 当前计算逻辑
```java
public CYBKLoanGuaranteeInfo getGuaranteeAmtAndRate(CYBKLoanGuaranteeInfo guaranteeInfo, Credit credit) {
    //计算 对客利率的等额本息还款金额
    List<RepayPlan> repayPlanList = PlanGenerator.genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST, 
        LocalDate.now(), credit.getLoanAmt(), credit.getCustomRate(), credit.getPeriods());
    BigDecimal totalAmt = repayPlanList.stream().map(item -> item.getPrincipal().add(item.getInterest()))
        .reduce(BigDecimal.ZERO, BigDecimal::add);

    // 计算对资利率的等额本息还款金额
    List<RepayPlan> repayBankPlanList = PlanGenerator.genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST, 
        LocalDate.now(), credit.getLoanAmt(), credit.getBankRate(), credit.getPeriods());
    BigDecimal totalBankAmt = repayBankPlanList.stream().map(item -> item.getPrincipal().add(item.getInterest()))
        .reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal guaranteeAmt = totalAmt.subtract(totalBankAmt); //担保费 = 对客总额 - 对资总额
}
```

#### 2.2.2 优势分析
- **使用动态利率**: 直接使用`credit.getBankRate()`，会自动使用动态定价利率
- **自动调整**: 当对资利率变小时，`totalBankAmt`减少，`guaranteeAmt`自动增加 ✅

### 2.3 Capital模块 - CYBKRepayService（提前结清融担费）

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/banks/cybk/service/CYBKRepayService.java`

#### 2.3.1 当前计算逻辑
```java
public BigDecimal getGuaranteeAmt(Loan loan, LoanReplan minPlan, BigDecimal existPrincipal, int principalDays) {
    //融担日利率 = (对客利率 - 对资利率) / 360
    BigDecimal guaranteeRate = loan.getCustomRate().subtract(loan.getBankRate()).divide(
        new BigDecimal(YEAR_DAYS), PROCESSING_POINT_NUM, RoundingMode.HALF_UP);

    // 提前结清融担费 = 剩余本金 * 资金实际占用天数 * 融担费日利率
    BigDecimal guaranteeFee = existPrincipal.multiply(new BigDecimal(principalDays)).multiply(guaranteeRate)
        .setScale(2, RoundingMode.HALF_UP);
    return guaranteeFee;
}
```

#### 2.3.2 优势分析
- **使用动态利率**: 直接使用`loan.getBankRate()`，会自动使用动态定价利率
- **自动调整**: 当对资利率变小时，`guaranteeRate`自动增加 ✅

### 2.4 Capital模块 - CYBKLoanService（融担费填充）

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/banks/cybk/service/CYBKLoanService.java`

#### 2.4.1 当前计算逻辑
```java
public void fillGuaranteeAmt(Loan loan, List<PlanItemVo> planItemVoList) {
    Credit credit = creditRepository.findById(loan.getCreditId()).orElseThrow();

    //计算 对客利率的等额本息还款金额
    List<RepayPlan> repayPlanList = PlanGenerator.genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST, 
        LocalDate.now(), loan.getLoanAmt(), credit.getCustomRate(), credit.getPeriods());

    planItemVoList.forEach(p -> {
        RepayPlan repayPlan = repayMap.get(p.getPeriod());
        // 融担费 = 对客还款额 - 对资还款额
        p.setGuaranteeAmt(repayPlan.getPrincipal().add(repayPlan.getInterest())
            .subtract(p.getPrincipalAmt()).subtract(p.getInterestAmt()));
    });
}
```

#### 2.4.2 优势分析
- **使用动态利率**: `p.getPrincipalAmt()`和`p.getInterestAmt()`基于动态定价利率计算
- **自动调整**: 当对资利率变小时，对资还款额减少，融担费自动增加 ✅

## 3. 融担费计算影响分析

### 3.1 对资利率变化的影响

假设场景：
- **对客利率**: 保持23.99%不变
- **对资利率**: 从15%变为12%（减少3%）

#### 3.1.1 融担费变化

| 计算方式 | 之前融担费 | 现在融担费 | 变化 |
|---------|-----------|-----------|------|
| 利率差计算 | 23.99% - 15% = 8.99% | 23.99% - 12% = 11.99% | +3% ✅ |
| 还款额差计算 | 对客总额 - 对资总额(15%) | 对客总额 - 对资总额(12%) | 增加 ✅ |

#### 3.1.2 收益影响

**以10万元12期为例**：
- **之前**: 融担费约8.99%年化
- **现在**: 融担费约11.99%年化
- **增加**: 约3%年化，符合预期 ✅

### 3.2 需要改造的地方

#### 3.2.1 Flow模块 - Rate36Service（❌ 需要改造）

**问题**: 使用固定的QhBank利率配置
**改造**: 改为使用loan中的动态利率

```java
// 改造前
QhBank qhBank = QhBank.getQhBankBy(loan.getBankChannel());
BigDecimal guaranteeRate = qhBank.getBankCustomRate().subtract(qhBank.getBankRate());

// 改造后
BigDecimal guaranteeRate = loan.getIrrRate().subtract(loan.getBankRate());
```

#### 3.2.2 Capital模块（✅ 无需改造）

- **CYBKLoanService.getGuaranteeAmtAndRate()**: 已使用动态利率
- **CYBKRepayService.getGuaranteeAmt()**: 已使用动态利率
- **CYBKLoanService.fillGuaranteeAmt()**: 已使用动态利率

## 4. 改造建议

### 4.1 需要改造的文件

| 序号 | 文件位置 | 方法 | 改造内容 |
|------|---------|------|---------|
| 1 | `Rate36Service.java` | `trialClearGuaranteeAmt()` | 使用loan中的动态利率替代QhBank固定利率 |

### 4.2 改造代码

```java
// Rate36Service.trialClearGuaranteeAmt()方法
public BigDecimal trialClearGuaranteeAmt(Loan loan, Integer period, List<RepayPlan> repayPlans, LocalDate date) {
    BigDecimal consultAmt;
    
    // 改造：使用loan中的动态利率
    BigDecimal guaranteeRate = loan.getIrrRate().subtract(loan.getBankRate());
    
    // 其他计算逻辑保持不变...
}
```

## 5. 总结

### 5.1 融担费自动调整机制

**当对资利率变小时**：
- **Capital模块**: 已经能够自动调整，融担费会增加 ✅
- **Flow模块**: 需要改造Rate36Service，使用动态利率 ❌

### 5.2 收益保障

通过使用动态定价利率，确保：
- **对资利率下降** → **融担费自动增加**
- **融担收益得到保障** ✅

### 5.3 改造工作量

**非常小**：只需要修改Rate36Service中的一行代码，将固定利率改为动态利率。
