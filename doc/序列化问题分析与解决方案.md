# Flow与Capital模块序列化问题分析与解决方案

## 问题描述

在Flow模块通过Feign调用Capital模块时，出现了boolean类型的`isSuccess()`方法被序列化成`success`字段的问题。

## 问题分析

### 1. 序列化包使用情况

#### Flow模块 (jh-loan-cash-flow)
- **fastjson2**: 2.0.32 (主要版本)
- **fastjson**: 2.0.43 (旧版本)
- **jackson-dataformat-xml**: Spring Boot默认版本
- **Jackson**: Spring Boot 3.x 默认序列化器

#### Capital模块 (jh-loan-cash-capital)
- **capital-core**: fastjson2 2.0.44, fastjson 2.0.43
- **capital-batch**: fastjson2 2.0.45, jackson-core (Spring Boot默认版本)

### 2. 问题根源

1. **版本不统一**: 不同模块使用了不同版本的fastjson2
2. **序列化器混用**: 同时存在Jackson和FastJSON两套序列化器
3. **Feign默认行为**: Spring Boot 3.x中Feign默认使用Jackson进行序列化/反序列化

### 3. 具体问题场景

在`RepayDetailJhJob.java`第106行：
```java
if (!(query.isSuccess() && query.getData().getStatus() == ProcessStatus.SUCCESS)) {
```

这里的`query`是`RestResult<ReccResultDto>`类型，通过Feign调用返回。

#### RestResult类定义
```java
public class RestResult<T> {
    private String code;
    private String msg;
    private T data;
    
    public boolean isSuccess() {
        return ResultCode.SUCCESS.getCode().equals(this.code);
    }
}
```

### 4. 序列化行为差异

#### Jackson序列化行为
- 对于`isSuccess()`方法，Jackson会将其识别为`success`属性
- 遵循JavaBean规范：`isXxx()`方法对应`xxx`属性

#### FastJSON序列化行为
- FastJSON对boolean类型的`isXxx()`方法处理可能不同
- 不同版本的FastJSON处理逻辑可能存在差异

## 解决方案

### 方案1：统一序列化器配置（推荐）

#### 1.1 为Feign配置统一的序列化器

创建Feign配置类：

```java
@Configuration
public class FeignConfig {
    
    @Bean
    public Encoder feignEncoder() {
        return new JacksonEncoder(objectMapper());
    }
    
    @Bean
    public Decoder feignDecoder() {
        return new JacksonDecoder(objectMapper());
    }
    
    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        // 配置属性命名策略，确保isSuccess()映射为success
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.LOWER_CAMEL_CASE);
        return mapper;
    }
}
```

#### 1.2 在RestResult类上添加注解

```java
public class RestResult<T> {
    private String code;
    private String msg;
    private T data;
    
    @JsonProperty("success")
    public boolean isSuccess() {
        return ResultCode.SUCCESS.getCode().equals(this.code);
    }
}
```

### 方案2：版本统一

#### 2.1 统一FastJSON版本

将所有模块的fastjson2版本统一为最新稳定版本：

```xml
<properties>
    <fastjson2.version>2.0.45</fastjson2.version>
</properties>

<dependency>
    <groupId>com.alibaba.fastjson2</groupId>
    <artifactId>fastjson2</artifactId>
    <version>${fastjson2.version}</version>
</dependency>
```

#### 2.2 移除旧版本依赖

移除fastjson 1.x版本的依赖，避免版本冲突。

### 方案3：显式字段映射

#### 3.1 修改RestResult类

```java
public class RestResult<T> {
    private String code;
    private String msg;
    private T data;
    
    // 添加success字段的getter/setter
    public boolean getSuccess() {
        return isSuccess();
    }
    
    public void setSuccess(boolean success) {
        // 可以为空实现，因为success是计算属性
    }
    
    public boolean isSuccess() {
        return ResultCode.SUCCESS.getCode().equals(this.code);
    }
}
```

## 推荐实施步骤

1. **立即解决**: 使用方案1.2，在RestResult类上添加`@JsonProperty("success")`注解
2. **中期优化**: 实施方案1.1，统一Feign的序列化配置
3. **长期规划**: 实施方案2，统一所有模块的序列化库版本

## 注意事项

1. **向后兼容**: 修改序列化行为时要考虑现有API的兼容性
2. **测试覆盖**: 修改后需要充分测试所有Feign调用
3. **文档更新**: 更新相关的API文档和接口说明
4. **监控告警**: 部署后密切监控相关接口的调用情况

## 验证方法

1. **单元测试**: 编写测试用例验证序列化/反序列化行为
2. **集成测试**: 测试Flow与Capital模块间的Feign调用
3. **日志监控**: 检查相关接口的调用日志和错误信息
