package com.jinghang.cash.modules.common;

import cn.hutool.core.util.ObjectUtil;
import com.jinghang.cash.enums.ResultCode;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.service.UploadFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/file")
public class FileController {

    @Autowired
    private UploadFileService fileService;

    @Value("${oss.bucket.name}")
    private String bucketName;

    /**
     * 文件上传接口
     * @param file 文件
     * @return 上传结果
     */
    @PostMapping("/upload")
    public RestResult<Object> upload(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return RestResult.fail(ResultCode.FILE_IS_EMPTY);
        }
        // 获取文件名称
        String originalFilename = file.getOriginalFilename();
        // 生成唯一文件名（防止文件覆盖）
        String fileName = UUID.randomUUID().toString().replaceAll("-", "");
        // 获取文件后缀
        String suffix = "";
        if (originalFilename != null) {
            suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        // 构建日期路径，
        String datePath = new SimpleDateFormat("yyyy/MM/dd/").format(new Date());
        // 构建文件完整路径
        String filePath = "upload" + "/" + datePath + fileName + suffix;
        try {
            fileService.uploadOss(bucketName,filePath,file.getInputStream());
            //String ossUri = fileService.getOssUrl(bucketName, filePath);
            return RestResult.success(filePath);
        } catch (IOException e) {
            return RestResult.fail(ResultCode.SYS_ERROR);
        }
    }

    /**
     *  获取文件URL
     * @param paths 文件URL
     */
    @PostMapping("/getFileUrl")
    public RestResult<Object> delete(@RequestBody List<String> paths) {
        List<String> urls = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(paths)){
            paths.forEach(x->{
                urls.add(fileService.getOssUrl(bucketName,x));
            });
        }
        return RestResult.success(urls);
    }

    /**
     * 删除文件接口
     * @param fileUrl 文件URL
     * @return 删除结果
     */
    @PostMapping("/delete")
    public RestResult<Object> delete(@RequestParam("fileUrl") String fileUrl) {
        fileService.delete(bucketName,fileUrl);
        return RestResult.success();
    }
}
