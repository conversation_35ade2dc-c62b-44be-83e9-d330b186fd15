package com.jinghang.cash.modules.project.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.cash.api.enums.AbleStatus;
import com.jinghang.cash.api.enums.ProjectType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 项目信息实体
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 10:41
 */
@TableName(value = "project_info")
@Getter
@Setter
public class ProjectInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 项目唯一编码
     */
    @TableField(value = "project_code")
    private String projectCode;

    /**
     * 项目名称
     */
    @TableField(value = "project_name")
    private String projectName;

    /**
     * 资产方编码 (关联资产方表)
     */
    @TableField(value = "flow_channel")
    private String flowChannel;

    /**
     * 融担方编码 (关联融担方表)
     */
    @TableField(value = "guarantee_code")
    private String guaranteeCode;

    /**
     * 资金方编码 (关联资金方表)
     */
    @TableField(value = "capital_channel")
    private String capitalChannel;

    /**
     * 项目类型编码 (关联项目类型表)
     */
    @TableField(value = "project_type_code")
    private String projectTypeCode;

    /**
     * 项目状态 (ENABLE/DISABLE)
     */
    @TableField(value = "enabled")
    private String enabled;

    /**
     * 项目开始日期
     */
    @TableField(value = "start_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startDate;

    /**
     * 项目结束日期
     */
    @TableField(value = "end_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endDate;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 版本号
     */
    @TableField(value = "revision", fill = FieldFill.INSERT)
    @Version
    private Integer revision;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @TableField(value = "updated_by", fill = FieldFill.UPDATE)
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;

    public void copy(ProjectInfo source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
