/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghang.cash.api.enums.AbleStatusExt;
import com.jinghang.cash.modules.project.domain.FlowConfig;
import com.jinghang.cash.modules.project.service.FlowConfigService;
import com.jinghang.cash.utils.PageResult;
import com.jinghang.cash.utils.PageUtil;
import com.jinghang.cash.utils.SecurityUtils;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.jinghang.ppd.api.dto.route.EnableFlowConfigDTO;
import lombok.RequiredArgsConstructor;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.modules.project.domain.dto.FlowConfigQueryCriteria;
import com.jinghang.cash.modules.project.mapper.FlowConfigMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* @description 服务实现
* <AUTHOR>
* @date 2025-08-22
**/
@Service
@RequiredArgsConstructor
public class FlowConfigServiceImpl extends ServiceImpl<FlowConfigMapper, FlowConfig> implements FlowConfigService {
    private static final Logger logger = LoggerFactory.getLogger(FlowConfigServiceImpl.class);

    private final FlowConfigMapper flowConfigMapper;

    @Override
    public PageResult<FlowConfig> queryAllPage(FlowConfigQueryCriteria criteria){
        logger.info("分页条件查询投诉参数:{}", JsonUtil.toJsonString(criteria));
        Page<FlowConfig> page = new Page<>(criteria.getPageNum(), criteria.getPageSize());
        LambdaQueryWrapper<FlowConfig> wrapper = new LambdaQueryWrapper<FlowConfig>();
        if(StringUtil.isNotBlank(criteria.getFlowName())){
            wrapper.like(FlowConfig::getFlowName,criteria.getFlowName());
        }
        if(StringUtil.isNotBlank(criteria.getFlowChannel())){
            wrapper.eq(FlowConfig::getFlowChannel,criteria.getFlowChannel());
        }
        if(StringUtil.isNotBlank(criteria.getEnabled())){
            wrapper.eq(FlowConfig::getEnabled,criteria.getEnabled());
        }
        page = flowConfigMapper.selectPage(page, wrapper);
        return PageUtil.toPage(page.getRecords(), page.getTotal());
    }

    @Override
    public List<FlowConfig> queryAll(FlowConfigQueryCriteria criteria){
        return flowConfigMapper.findAll(criteria);
    }

    @Override
    public FlowConfig queryFlowConfigInfo(String id) {
        return flowConfigMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(FlowConfig resources) {
        String id = "F" + System.currentTimeMillis() + String.valueOf(Math.random()).substring(2,5) ;
        resources.setId(id);
        resources.setCreatedBy(String.valueOf(SecurityUtils.getCurrentUserId()));
        resources.setCreatedTime(LocalDateTime.now());
        resources.setEnabled(AbleStatusExt.notEffective.name());
        flowConfigMapper.insert(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(FlowConfig resources) {
        FlowConfig flowConfig = getById(resources.getId());
        flowConfig.copy(resources);
        resources.setUpdatedBy(String.valueOf(SecurityUtils.getCurrentUserId()));
        resources.setUpdatedTime(LocalDateTime.now());
        flowConfigMapper.updateById(flowConfig);
    }

    @Override
    public void enable(EnableFlowConfigDTO dto) {
        FlowConfig flowConfig = getById(dto.getFlowConfigId());
        flowConfig.setUpdatedBy(String.valueOf(SecurityUtils.getCurrentUserId()));
        flowConfig.setUpdatedTime(LocalDateTime.now());
        flowConfig.setEnabled(dto.getEnabled());
        flowConfigMapper.updateById(flowConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(List<String> ids) {
        flowConfigMapper.deleteBatchIds(ids);
    }

    @Override
    public void download(List<FlowConfig> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (FlowConfig flowConfig : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("流量渠道", flowConfig.getFlowChannel());
            map.put("启用状态", flowConfig.getEnabled());
            map.put("备注", flowConfig.getRemark());
            map.put("乐观锁", flowConfig.getRevision());
            map.put("创建人", flowConfig.getCreatedBy());
            map.put("创建时间", flowConfig.getCreatedTime());
            map.put("更新人", flowConfig.getUpdatedBy());
            map.put("更新时间", flowConfig.getUpdatedTime());
            map.put("资产方简称", flowConfig.getFlowNameShort());
            map.put("资产方主体全称", flowConfig.getFlowName());
            map.put("资方简介", flowConfig.getFlowDesc());
            map.put("联系人", flowConfig.getContactPerson());
            map.put("联系电话", flowConfig.getContactPhone());
            map.put("邮箱地址", flowConfig.getEmailAddress());
            list.add(map);
        }
        //FileUtil.downloadExcel(list, response);
    }
}