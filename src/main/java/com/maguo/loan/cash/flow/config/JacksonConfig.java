package com.maguo.loan.cash.flow.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.time.LocalDateTime;

/**
 * Jackson配置类
 * 解决LocalDateTime反序列化问题
 *
 * 使用FlexibleLocalDateTimeDeserializer支持多种日期格式：
 * - ISO格式：yyyy-MM-dd'T'HH:mm:ss
 * - 空格格式：yyyy-MM-dd HH:mm:ss (Cash-Manage模块使用)
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@Configuration
public class JacksonConfig {

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        // 注册JavaTimeModule
        JavaTimeModule javaTimeModule = new JavaTimeModule();

        // 使用灵活的LocalDateTime反序列化器，支持多种日期格式
        javaTimeModule.addDeserializer(LocalDateTime.class, new FlexibleLocalDateTimeDeserializer());

        mapper.registerModule(javaTimeModule);
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        // 重要：设置属性命名策略，确保isSuccess()方法能正确序列化
        // 这样isSuccess()方法会被识别为success属性，保持与之前的兼容性
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.LOWER_CAMEL_CASE);

        return mapper;
    }
}
