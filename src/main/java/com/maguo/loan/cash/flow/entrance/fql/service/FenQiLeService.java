package com.maguo.loan.cash.flow.entrance.fql.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.jinghang.ppd.api.dto.repay.RepayApplyDto;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.convert.ManageConvert;
import com.maguo.loan.cash.flow.dto.OfflineRepayApplyRequest;
import com.maguo.loan.cash.flow.dto.OnlineRepayApplyRequest;
import com.maguo.loan.cash.flow.entity.BindCardRecord;
import com.maguo.loan.cash.flow.entity.BindCardRelation;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.FqlBillDetails;
import com.maguo.loan.cash.flow.entity.FqlBillDetailsOtherInfo;
import com.maguo.loan.cash.flow.entity.FqlCreditApplyRecord;
import com.maguo.loan.cash.flow.entity.FqlLoanApplyRecord;
import com.maguo.loan.cash.flow.entity.FqlRebindRecord;
import com.maguo.loan.cash.flow.entity.FqlRepayApplyRecord;
import com.maguo.loan.cash.flow.entity.FqlSepInInfo;
import com.maguo.loan.cash.flow.entity.FqlSepOutInfo;
import com.maguo.loan.cash.flow.entity.FqlUserBankCard;
import com.maguo.loan.cash.flow.entity.FqlWithholdDetail;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.OutWithholdFlow;
import com.maguo.loan.cash.flow.entity.OutWithholdOutInfo;
import com.maguo.loan.cash.flow.entity.OutWithholdShareInfo;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.UserBankCard;
import com.maguo.loan.cash.flow.entity.UserContactInfo;
import com.maguo.loan.cash.flow.entity.UserDevice;
import com.maguo.loan.cash.flow.entity.UserFace;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserOcr;
import com.maguo.loan.cash.flow.entity.UserRegister;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.entity.UserRiskRecordExternal;
import com.maguo.loan.cash.flow.entity.common.ProjectProductMapping;
import com.maguo.loan.cash.flow.entrance.common.covert.ProjectConfigMapperHelper;
import com.maguo.loan.cash.flow.entrance.common.exception.CommonApiBizException;
import com.maguo.loan.cash.flow.entrance.common.exception.CommonApiResultCode;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.entrance.common.strategy.ProjectCodeMapper;
import com.maguo.loan.cash.flow.entrance.fql.config.FqlConfig;
import com.maguo.loan.cash.flow.entrance.fql.convert.FenQiLeConvert;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.CreditQueryRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.CreditQueryResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.FenQiLeCreditApplyRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.FenQiLeCreditApplyResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.credit.UpLoadInfo;
import com.maguo.loan.cash.flow.entrance.fql.dto.loan.FenQiLeRepayPlan;
import com.maguo.loan.cash.flow.entrance.fql.dto.loan.LoanApplyRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.loan.LoanApplyResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.loan.LoanQueryRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.loan.LoanQueryResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.loan.RepayPlanQueryRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.loan.RepayPlanQueryResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRealReturnNoticeRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRealReturnNoticeResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRealReturnResultRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRealReturnResultResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRepayParamer;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRepayRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRepayResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRepayResultRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRepayResultResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRepayTrialRequest;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.FenQiLeRepayTrialResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.BillDetailsInfo;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.BindCardInfo;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.OtherInfo;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.SepInInfo;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.SepOutInfo;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.request.WithholdDetailInfo;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.response.RpyDetailsResponse;
import com.maguo.loan.cash.flow.entrance.fql.dto.repay.response.WithholdDetailResponse;
import com.maguo.loan.cash.flow.entrance.fql.enums.FenQiLeRepayChannelEnum;
import com.maguo.loan.cash.flow.entrance.fql.enums.FenQiLeRepayStatusEnum;
import com.maguo.loan.cash.flow.entrance.fql.enums.FenQiLeRepayTrialReturnCodeEnum;
import com.maguo.loan.cash.flow.entrance.fql.exception.FenQiLeBizException;
import com.maguo.loan.cash.flow.entrance.fql.exception.FenQiLeResultCode;
import com.maguo.loan.cash.flow.entrance.lvxin.exception.LvxinBizException;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.BoundSide;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.LoanPurpose;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.PreOrderState;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.ProtocolChannel;
import com.maguo.loan.cash.flow.enums.RepayMode;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.enums.RiskChannel;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.enums.WriteOffTypeEnum;
import com.maguo.loan.cash.flow.remote.cardbin.CardBin;
import com.maguo.loan.cash.flow.remote.cardbin.impl.AlipayCardBinService;
import com.maguo.loan.cash.flow.repository.BindCardRecordRepository;
import com.maguo.loan.cash.flow.repository.BindCardRelationRepository;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.FqlBillDetailsOtherInfoRepository;
import com.maguo.loan.cash.flow.repository.FqlBillDetailsRepository;
import com.maguo.loan.cash.flow.repository.FqlCreditApplyRecordRepository;
import com.maguo.loan.cash.flow.repository.FqlLoanApplyRecordRepository;
import com.maguo.loan.cash.flow.repository.FqlRebindRecordRepository;
import com.maguo.loan.cash.flow.repository.FqlRepayApplyRecordRepository;
import com.maguo.loan.cash.flow.repository.FqlSepInInfoRepository;
import com.maguo.loan.cash.flow.repository.FqlSepOutInfoRepository;
import com.maguo.loan.cash.flow.repository.FqlUserBankCardRepository;
import com.maguo.loan.cash.flow.repository.FqlWithholdDetailRepository;
import com.maguo.loan.cash.flow.repository.LoanFailFollowRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderBindCardRecordRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.OutWithholdFlowRepository;
import com.maguo.loan.cash.flow.repository.OutWithholdOutInfoRepository;
import com.maguo.loan.cash.flow.repository.OutWithholdShareInfoRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.ProjectProductMappingRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import com.maguo.loan.cash.flow.repository.UserFaceRepository;
import com.maguo.loan.cash.flow.repository.UserOcrRepository;
import com.maguo.loan.cash.flow.repository.UserRegisterRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordExternalRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.service.CheckService;
import com.maguo.loan.cash.flow.service.FileService;
import com.maguo.loan.cash.flow.service.LockService;
import com.maguo.loan.cash.flow.service.Locker;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.OrderService;
import com.maguo.loan.cash.flow.service.RepayService;
import com.maguo.loan.cash.flow.service.TrialService;
import com.maguo.loan.cash.flow.service.UserFileService;
import com.maguo.loan.cash.flow.service.UserService;
import com.maguo.loan.cash.flow.service.agreement.AgreementService;
import com.maguo.loan.cash.flow.service.bound.CapitalCardService;
import com.maguo.loan.cash.flow.service.bound.PlatformCardService;
import com.maguo.loan.cash.flow.service.bound.exchange.ExchangeCardApplyReq;
import com.maguo.loan.cash.flow.service.common.loan.LoanCommonCheckService;
import com.maguo.loan.cash.flow.service.event.listener.RiskResultEventListener;
import com.maguo.loan.cash.flow.util.DateUtil;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 分期乐 业务服务类
 * @Date 2024/8/6 14:40
 * @Version v1.0
 **/
@Slf4j
@Service
public class FenQiLeService {

    private static final Logger logger = LoggerFactory.getLogger(FenQiLeService.class);

    public static final int LOCK_WAIT_SECOND = 2;
    public static final int LOCK_RELEASE_SECOND = 8;
    public static final long SEVEN = 7L;


    private final Integer thirty = 30;
    public static final Duration DEFAULT_LOCK_RELEASE_TIME = Duration.ofSeconds(8);
    public static final String APPROVAL_APPLY = "_approval_apply:";

    public static final String LOAN_APPLY = "_loan_apply:";

    @Value(value = "${oss.bucket.name}")
    private String ossBucket;
    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;
    @Autowired
    private UserRiskRecordExternalRepository userRiskRecordExternalRepository;
    @Autowired
    private PreOrderRepository preOrderRepository;
    @Autowired
    private TrialService trialService;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private RepayPlanRepository repayPlanRepository;
    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;
    @Autowired
    private FqlRepayApplyRecordRepository fqlRepayApplyRecordRepository;
    @Autowired
    private FqlUserBankCardRepository fqlUserBankCardRepository;
    @Autowired
    private FqlSepOutInfoRepository fqlSepOutInfoRepository;
    @Autowired
    private FqlSepInInfoRepository fqlSepInInfoRepository;
    @Autowired
    private FqlWithholdDetailRepository fqlWithholdDetailRepository;
    @Autowired
    private FqlBillDetailsRepository fqlBillDetailsRepository;
    @Autowired
    private FqlBillDetailsOtherInfoRepository fqlBillDetailsOtherInfoRepository;
    @Autowired
    private UserBankCardRepository userBankCardRepository;
    @Autowired
    private RepayService repayService;
    @Autowired
    private LockService lockService;
    @Autowired
    private FqlRebindRecordRepository fqlRebindRecordRepository;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private PlatformCardService platformCardService;
    @Autowired
    private CapitalCardService capitalCardService;
    @Autowired
    private OutWithholdFlowRepository outWithholdFlowRepository;
    @Autowired
    private OutWithholdOutInfoRepository outWithholdOutInfoRepository;
    @Autowired
    private OutWithholdShareInfoRepository outWithholdShareInfoRepository;
    @Autowired
    private FqlCreditApplyRecordRepository fqlCreditApplyRecordRepository;
    @Autowired
    private CheckService checkService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserRegisterRepository userRegisterRepository;
    @Autowired
    private MqService mqService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private FqlLoanApplyRecordRepository fqlLoanApplyRecordRepository;
    @Autowired
    private AlipayCardBinService alipayCardBinService;
    @Autowired
    private BindCardRecordRepository bindCardRecordRepository;
    @Autowired
    private BindCardRelationRepository relationRepository;
    @Autowired
    private OrderBindCardRecordRepository orderBindCardRecordRepository;
    @Autowired
    private LoanFailFollowRepository loanFailFollowRepository;
    @Autowired
    private FileService fileService;
    @Autowired
    private FqlConfig fqlConfig;
    @Autowired
    private UserOcrRepository userOcrRepository;
    @Autowired
    private UserFaceRepository userFaceRepository;
    @Autowired
    private UserFileService userFileService;
    @Autowired
    private SftpUtils sftpUtils;
    @Autowired
    private AgreementService agreementService;
    @Autowired
    private RiskResultEventListener eventListener;
    @Autowired
    private ProjectCodeMapper projectCodeMapper;

    @Autowired
    private ProjectConfigMapperHelper projectConfigMapperHelper;

    @Autowired
    private LoanCommonCheckService loanCommonCheckService;

    @Autowired
    private ProjectInfoService projectInfoService;

    @Autowired
    private ProjectProductMappingRepository projectProductMappingRepository ;

    /**
     * 授信申请进件
     *
     * @param request
     * @return
     */
    public FenQiLeCreditApplyResponse creditApply(FenQiLeCreditApplyRequest request) {
        Map<String, String> params = new HashMap<>();
        params.put("flowSource", FlowChannel.FQLQY001.name());
        params.put("partnerCode", request.getPartnerCode());
        String productCode = projectCodeMapper.getProjectCode("FLOW", params);
        Optional<ProjectProductMapping> project =
            projectProductMappingRepository.findByProductCode(productCode);
        String projectCode = project.get().getProjectCode();
        request.setProjectCode(projectCode);
        String creditApplyId = request.getCreditApplyId();//分期乐授信申请id
        Locker lock = lockService.getLock(FlowChannel.FQLQY001.name() + APPROVAL_APPLY + creditApplyId + request.getPartnerCode());
        try {
            boolean locked = lock.tryLock(Duration.ZERO, DEFAULT_LOCK_RELEASE_TIME);
            if (!locked) {
                return FenQiLeCreditApplyResponse.fail("重复提交");
            }

            //判断是否重复提交
            FqlCreditApplyRecord byCreditApplyId = fqlCreditApplyRecordRepository.findByCreditApplyId(creditApplyId);
            if (Objects.nonNull(byCreditApplyId)){
                return FenQiLeCreditApplyResponse.fail("重复提交");
            }

            // 预检查文件是否上传完毕
            checkFtpFileReady(request);

            //查询预订单，是否是可以进件的数据
            PreOrder preOrder = preOrderRepository.findByOrderNoAndFlowChannel(creditApplyId, FlowChannel.FQLQY001).orElse(new PreOrder());

            preOrder.setProjectCode(projectCode);
            if (PreOrderState.AUDIT_REJECT == preOrder.getPreOrderState()) {
                return FenQiLeCreditApplyResponse.fail("订单已拒绝");
            }
            //是否内部风控拒绝
            List<UserRiskRecord> userRiskRecords = queryThirtyDayRiskRejectRecord(projectCode,request.getIdentiNo(), FlowChannel.FQLQY001);
            if (!CollectionUtils.isEmpty(userRiskRecords)) {
                return FenQiLeCreditApplyResponse.fail("综合评分不足");
            }

            // 30天内授信失败检验，目前资方渠道只有长银
            List<Order> orders = queryThirtyDayCreditFailRecord(projectCode,request.getIdentiNo(), FlowChannel.FQLQY001, BankChannel.CYBK);

            if (!CollectionUtils.isEmpty(orders)) {
                return FenQiLeCreditApplyResponse.fail("综合评分不足");
            }
            //处理中
            if (PreOrderState.AUDITING == preOrder.getPreOrderState()) {
                return FenQiLeCreditApplyResponse.processing();
            }
            // 转换预订单数据，目前资方渠道只有长银
            preOrder = FenQiLeConvert.INSTANCE.toPreOrder(preOrder, request,projectConfigMapperHelper);

            //保存预订单
            preOrder = preOrderRepository.save(preOrder);

            //保存分期乐授信申请记录
            FqlCreditApplyRecord fqlCreditApplyRecord = fqlCreditApplyRecordRepository.findByCreditApplyId(request.getCreditApplyId());
            if (fqlCreditApplyRecord == null) {
                //新增
                fqlCreditApplyRecord = FenQiLeConvert.INSTANCE.toCreditApplyRecord(request);
            } else {
                //更新
                fqlCreditApplyRecord = FenQiLeConvert.INSTANCE.toCreditApplyRecord(fqlCreditApplyRecord, request);
            }
            fqlCreditApplyRecordRepository.save(fqlCreditApplyRecord);

            //判断是否存在在途订单
            String onOrder = checkService.onOrderOrRiskByCertNo(preOrder);
            if (StringUtil.isNotBlank(onOrder)) {
                preOrder.setPreOrderState(PreOrderState.AUDIT_REJECT);
                preOrder.setIsReject(WhetherState.Y);
                preOrder.setRemark(onOrder);
                preOrderRepository.save(preOrder);
                return FenQiLeCreditApplyResponse.fail(onOrder);
            }
            //注册用户
            UserRegister userRegister = userService.findUserRegisterByOpenId(preOrder.getOpenId(), preOrder.getFlowChannel());
            if (Objects.isNull(userRegister)) {
                userRegister = userService.registerRecord(preOrder.getMobile(), preOrder.getOpenId(), preOrder.getFlowChannel());
            }
            //user_info
            UserInfo userInfo = FenQiLeConvert.INSTANCE.toUserInfo(preOrder, fqlCreditApplyRecord);
            //user_ocr
            UserOcr userOcr = createUserOcr(preOrder, fqlCreditApplyRecord);
            //user_face
            UserFace userFace = createUserFace(preOrder, fqlCreditApplyRecord);
            //device
            UserDevice userDevice = createUserDevice(fqlCreditApplyRecord);
            //contactInfos
            List<UserContactInfo> contactInfos = createUserContactInfos(fqlCreditApplyRecord);
            //添加项目唯一编码 add by yunhengtong 20250821
            UserRiskRecordExternal riskRecord = userService.externalRegister(userInfo, userOcr, userFace, userDevice, contactInfos, FlowChannel.FQLQY001, preOrder.getApplyChannel(), preOrder, RiskChannel.BW, projectCode);
            String userId = riskRecord.getUserId();
            //保存风控id
            preOrder.setRiskId(riskRecord.getId());
            preOrder.setOpenId(userId);
            preOrder = preOrderRepository.saveAndFlush(preOrder);
            //填充userId
            userRegister.setUserId(userId);
            userRegisterRepository.save(userRegister);
            // 异步影像件下载
            logger.info("异步影像件下载开始：");
            mqService.submitFqlCreditUserFileDownload(fqlCreditApplyRecord.getId());
            logger.info("异步影像件下载结束：");

            //更新预订单为审核中
            preOrder.setPreOrderState(PreOrderState.AUDITING);
            preOrderRepository.saveAndFlush(preOrder);

            return FenQiLeCreditApplyResponse.processing();
        } catch (FenQiLeBizException e) {
            logger.error("分期乐授信失败,creditApplyId:" + creditApplyId, e);
            return FenQiLeCreditApplyResponse.fail("申请失败，系统异常");
        } catch (Exception e) {
            logger.error("分期乐授信失败,creditApplyId:" + creditApplyId, e);
            return FenQiLeCreditApplyResponse.fail("申请失败，系统异常");
        }
    }

    private List<UserRiskRecord> queryThirtyDayRiskRejectRecord(String projectCode,String idCard, FlowChannel flowChannel) {
        ProjectInfoDto projectInfoDto = projectInfoService.queryProjectInfo(projectCode);
        int creditLockDays = projectInfoDto.getElements().getCreditLockDays();
        LocalDateTime failCreditDate = LocalDateTime.now().minusDays(creditLockDays);
        return userRiskRecordRepository.queryThirtyDayRiskRejectRecord(idCard, failCreditDate, AuditState.REJECT, flowChannel);
    }

    public List<Order> queryThirtyDayCreditFailRecord(String projectCode,String certNo, FlowChannel flowChannel, BankChannel bankChannel) {
        ProjectInfoDto projectInfoDto = projectInfoService.queryProjectInfo(projectCode);
        int creditLockDays = projectInfoDto.getElements().getCreditLockDays();
        LocalDateTime failCreditDate = LocalDateTime.now().minusDays(creditLockDays);
        return orderRepository.queryThirtyDayCreditFailRecordLX(OrderState.CREDIT_FAIL, certNo, failCreditDate, flowChannel, bankChannel);
    }

    public List<UpLoadInfo> parseUpLoadInfoJson(String jsonString) {
        // 处理空值或特殊值
        if (StringUtils.isBlank(jsonString) || "[{}]".equals(jsonString)) {
            return Collections.emptyList();
        }

        try {
            return JSON.parseArray(jsonString, UpLoadInfo.class);
        } catch (Exception e) {
            logger.error("授信申请影响信息转换异常: {}", jsonString, e);
            return Collections.emptyList();
        }
    }

    private void checkFtpFileReady(FenQiLeCreditApplyRequest request) {

        try {
            sftpUtils.fqlFileExists(getFtpUri(request.getUploadInfo()));
        } catch (Exception e) {
            logger.error("与检查影像信息不存在", e);
            if (e.getMessage().contains("No such file")) {
                throw new LvxinBizException("检查证件信息不完整,请先上传证件信息");
            }
            throw new LvxinBizException("检查证件信息不完整");
        }
    }


    /**
     * 查询授信结果
     *
     * @param creditQueryRequest
     * @return CreditQueryResponse
     */
    public CreditQueryResponse creditResultQuery(CreditQueryRequest creditQueryRequest) {
        try {
            //查询预订单
            String orderNo = creditQueryRequest.getApplyId();
            PreOrder preOrder = preOrderRepository.findByOrderNoAndFlowChannel(orderNo, FlowChannel.FQLQY001).orElse(null);
            if (preOrder == null) {
                logger.error("查询授信结果找不到订单,applyId:{}", creditQueryRequest.getApplyId());
                return CreditQueryResponse.unknowOrder("查询不到订单");
            }

            switch (preOrder.getPreOrderState()) {
                case INIT, AUDITING -> {
                    return CreditQueryResponse.processing();// 审核中
                }
                case AUDIT_PASS -> {
                    Order order = orderService.findByRiskId(preOrder.getRiskId());
                    if (Objects.isNull(order)) {
                        //风控通过,可能订单还没保存
                        return CreditQueryResponse.processing();// 审核中
                    }

                    if (OrderState.LOAN_CANCEL == order.getOrderState()) {
                        return CreditQueryResponse.fail();// 授信过期
                    }
                    return CreditQueryResponse.success();
                }
                case AUDIT_REJECT -> {
                    return CreditQueryResponse.fail(preOrder.getRemark());
                }
                default -> {
                    return CreditQueryResponse.processing();
                }
            }
        } catch (Exception e) {
            logger.error("分期乐授信申请查询失败,applyId:{}" + creditQueryRequest.getApplyId(), e);
            return CreditQueryResponse.fail("查询失败，系统异常");
        }
    }

    public LoanApplyResponse loanApply(LoanApplyRequest request) {
        String applyId = request.getApplyId();//资产申请号
        Order order = orderRepository.findTopByOuterOrderIdAndFlowChannel(applyId, FlowChannel.FQLQY001);
        //统一校验放款阶段参数
        loanCommonCheckService.checkLoanParameters(order, request.getRepayType());

        Locker lock = lockService.getLock(FlowChannel.FQLQY001.name() + LOAN_APPLY + applyId + request.getPartnerCode());
        try {
            boolean locked = lock.tryLock(Duration.ZERO, DEFAULT_LOCK_RELEASE_TIME);
            if (!locked) {
                return LoanApplyResponse.fail("重复提交");
            }
            FqlLoanApplyRecord fqlLoanApplyRecord = fqlLoanApplyRecordRepository.findByApplyIdAndPartnerCode(applyId, request.getPartnerCode());
            //查询是否已经存在放款申请数据，存在就返回成功
            if (!Objects.isNull(fqlLoanApplyRecord)) {
                return LoanApplyResponse.processing();
            }
            if (OrderState.AUDIT_PASS != order.getOrderState()) {
                logger.error("【新流程】授信状态为:{},无法发起借款,orderId:{},applyId:{}", order.getOrderState(), order.getId(), applyId);
                return LoanApplyResponse.fail();
            }
            if (Objects.isNull(fqlLoanApplyRecord)) {
                fqlLoanApplyRecord = FenQiLeConvert.INSTANCE.toLoanApplyRecord(request);
            } else {
                fqlLoanApplyRecord = FenQiLeConvert.INSTANCE.toLoanApplyRecord(fqlLoanApplyRecord, request);
            }
            fqlLoanApplyRecordRepository.save(fqlLoanApplyRecord);
            //直接发起放款
            BindCardRecord finalRecord = new BindCardRecord();
            finalRecord.setUserId(order.getUserId());
            finalRecord.setState(ProcessState.PROCESSING);
            // 通联
            finalRecord.setChannel(ProtocolChannel.ALLIN_PAY);
            finalRecord.setCertNo(order.getCertNo());//从订单表中取
            // 默认收款卡即为还款卡，还款人卡信息
            finalRecord.setPhone(request.getMobileNo());
            finalRecord.setName(order.getName());//从订单表中取
            finalRecord.setBankCardNo(request.getDebitAccountNo());
//            finalRecord.setAgreeNo(request.getChannelRepayId());//绑卡成功协议号不传，capital需要额外处理
            //调用接口查询卡归属码值
            CardBin query = alipayCardBinService.query(request.getDebitAccountNo());
            if (ObjectUtils.isEmpty(query)) {
                return LoanApplyResponse.fail("不支持的银行卡");
            }
            finalRecord.setBankCode(query.getBankAbbr());
            finalRecord.setBankName(query.getName());
            BindCardRecord save = bindCardRecordRepository.save(finalRecord);
            BindCardRelation bindCardRelation = new BindCardRelation();
            bindCardRelation.setRelatedId(save.getId());
            bindCardRelation.setBindStage(LoanStage.LOAN);
            bindCardRelation.setBindCardApplyId(finalRecord.getId());
            bindCardRelation.setUserId(order.getUserId());

            relationRepository.save(bindCardRelation);
            // 绑卡成功, 插入用户绑卡记录
            UserBankCard userBankCard = new UserBankCard();
            userBankCard.setUserId(order.getUserId());
            userBankCard.setCardNo(request.getDebitAccountNo());
            userBankCard.setCardName(request.getDebitAccountName());
            userBankCard.setPhone(request.getMobileNo());
            userBankCard.setCertNo(order.getCertNo());//从订单表中取
            userBankCard.setChannel(ProtocolChannel.ALLIN_PAY);
            userBankCard.setMerchantNo("FQLQY001");//lvxin
//            userBankCard.setAgreeNo(request.getChannelRepayId());//绑卡协议号不传，capital需要额外处理
            userBankCard.setBankCode(finalRecord.getBankCode());
            userBankCard.setBankName(finalRecord.getBankName());
            userBankCardRepository.save(userBankCard);
            //借款用途
            order.setLoanPurpose(LoanPurpose.LoanPurposeByString(request.getLoanUse()));
            order.setId(order.getId());
            order.setLoanCardId(userBankCard.getId());
            order.setOrderSubmitState(WhetherState.Y);
            Optional<PreOrder> preOrder = preOrderRepository.findByOrderNo(order.getOuterOrderId());
            if(preOrder.isPresent()){
                order.setProjectCode(preOrder.get().getProjectCode());
            }
            order = orderRepository.save(order);
            // 调用参数校验服务
            orderService.apply(order.getId(), order.getRightsMarking());

            //保存绑卡baof的卡信息
            orderBindCardRecordRepository.findById(order.getId()).ifPresent(record -> {
                record.setFirstCardId(userBankCard.getId());
                orderBindCardRecordRepository.save(record);
            });
            //返回处理中
            return LoanApplyResponse.processing();
        } catch (InterruptedException e) {
            return LoanApplyResponse.fail("系统异常,请联系管理员");
        } catch (Exception e) {
            logger.error("分期乐放款失败,applyId:" + applyId, e);
            return LoanApplyResponse.fail("系统异常,请联系管理员");
        } finally {
            lock.unlock();
        }
    }

    public LoanQueryResponse loanQuery(LoanQueryRequest request) {
        String applyId = request.getApplyId();//资产申请号
        try {
            Loan loan;
            Order order = orderRepository.findByOuterOrderId(applyId);
            if (Objects.isNull(order)) {
                loan = loanRepository.findById(applyId).orElse(null);
            } else {
                loan = loanRepository.findByOrderId(order.getId());
            }
            if (Objects.isNull(loan) && Objects.isNull(order)) {
                logger.error("订单不存在,orderNo:" + applyId);
                return LoanQueryResponse.fail("订单不存在");
            }
            return queryLoanResult(loan, order);
        } catch (Exception e) {
            logger.error("分期乐放款结果查询失败,applyId:" + applyId, e);
            return LoanQueryResponse.fail("订单不存在");
        }
    }

    /**
     * 查询放款结果
     *
     * @param loan
     * @param order
     * @return
     */
    public LoanQueryResponse queryLoanResult(Loan loan, Order order) {
        try {
            if (Objects.isNull(order)) {
                order = orderRepository.findById(loan.getOrderId()).orElse(null);
                if (order == null) {
                    return LoanQueryResponse.fail("订单不存在");
                }
            }
            switch (order.getOrderState()) {
                case INIT, LOANING:
                    return LoanQueryResponse.processing();
                case LOAN_FAIL:
                    AtomicReference<String> failReasonRef = new AtomicReference<>("放款失败");
                    // TODO 需要确定
                    //封禁期:D+1天的早上7点
                    if (order.getRemark() != null && order.getRemark().contains("头寸不足")) {
                        failReasonRef.set(order.getRemark());
                    } else {
                        if (Objects.nonNull(loan)) {
                            loanFailFollowRepository.findByLoanId(loan.getId()).ifPresent(follow -> {
                                failReasonRef.set(follow.getFailReason());
                            });
                        }
                    }
                    return LoanQueryResponse.fail(failReasonRef.get());
                case LOAN_PASS:
                    String loanTime = DateUtil.formatLocalDateTime(loan.getLoanTime());
                    return LoanQueryResponse.success(loanTime, loan.getAmount(), loan.getId());
            }

            return LoanQueryResponse.processing();
        } catch (Exception e) {
            logger.error("分期乐放款结果查询异常", e);
            return LoanQueryResponse.fail("系统异常,请联系管理员");
        }

    }

    public RepayPlanQueryResponse repayPlanQuery(RepayPlanQueryRequest request) {
        RepayPlanQueryResponse response = new RepayPlanQueryResponse();
        String applyId = request.getApplyId();
        try {
            String loanId = request.getCapitalLoanNo();
            if (StringUtil.isBlank(loanId)) {
                response.setStatus(1);
                response.setMsg("[loanId]不能为空");
                return response;
            }
            Loan loan = loanRepository.findById(loanId).orElseThrow(() -> new LvxinBizException("订单不存在"));
            if (ProcessState.SUCCEED != loan.getLoanState()) {
                response.setStatus(1);
                response.setMsg("放款状态非成功");
                return response;
            }
            List<RepayPlan> repayPlanList = repayPlanRepository.findByLoanId(loanId);
            if (CollectionUtils.isEmpty(repayPlanList)) {
                response.setStatus(1);
                response.setMsg("还款计划还未生成");
                return response;
            } else {
                //构建分期乐还款计划
                return setFenQiLeRepayPlan(repayPlanList, response, loan);
            }
        } catch (Exception e) {
            logger.error("分期乐查询还款计划失败，applyId：" + applyId, e);
            response.setStatus(1);
            response.setMsg("系统异常,请联系管理员");
            return response;
        }
    }

    /**
     * 构建分期乐还款计划
     *
     * @param repayPlanList
     * @param response
     */
    private RepayPlanQueryResponse setFenQiLeRepayPlan(List<RepayPlan> repayPlanList, RepayPlanQueryResponse response, Loan loan) {
        //当前时间
        LocalDate today = LocalDate.now();
        //放款时间
        String loanTime = DateUtil.formatLocalDateTime(loan.getLoanTime());
        final BigDecimal totalLoanAmount = loan.getAmount();
        AtomicReference<BigDecimal> remainingPrincipal = new AtomicReference<>(totalLoanAmount);

        List<FenQiLeRepayPlan> repayPlans = repayPlanList.stream()
            .sorted(Comparator.comparing(RepayPlan::getPeriod))//先对还款计划进行排序，避免计算本期剩余本金时计算错误期数
            .map(item -> {
                FenQiLeRepayPlan repayPlan = new FenQiLeRepayPlan();
                repayPlan.setCapitalLoanNo(item.getLoanId());//资方放款编号
                repayPlan.setLoanTerm(item.getPeriod());//期次
                repayPlan.setPaymentTime(loanTime);//放款时间
                repayPlan.setExpectRepayDate(DateUtil.formatLocalDate(item.getPlanRepayDate()));//应还日
                if (RepayState.NORMAL.name().equals(item.getCustRepayState())) {
                    //未还
                    if (today.isAfter(item.getPlanRepayDate())) {
                        //逾期
                        repayPlan.setRepayStatus(20);//当期还款状态
                    } else {
                        repayPlan.setRepayStatus(0);//当期还款状态，
                    }
                } else {
                    //已还
                    repayPlan.setRepayStatus(10);//当期还款状态
                }
                repayPlan.setExpectRepayAmount(item.getAmount());//应还总额
                repayPlan.setExpectRepayPrincipal(item.getPrincipalAmt());//应还本金
                repayPlan.setExpectRepayInterest(item.getInterestAmt());//应还利息
                repayPlan.setExpectRepayPenaltyInterest(item.getPenaltyAmt());//应还罚息
                repayPlan.setExpectRepayGuarantee(item.getGuaranteeAmt());//应还担保费
//            repayPlan.setExpectRepayCreditFee();//应还信用评估费
//            repayPlan.setExpectGranteeConsultServiceFee();//应还担保咨询服务费
//            repayPlan.setExtendFields("");//扩展字段
                // 正确计算剩余本金
                BigDecimal currentRemaining = remainingPrincipal.get();
                remainingPrincipal.set(currentRemaining.subtract(item.getPrincipalAmt())); // 更新为下期剩余本金
                repayPlan.setRemainingPrincipal(remainingPrincipal.get()); // 本期剩余本金（还款前）,总借款本金-已还本金

                return repayPlan;
            }).sorted(Comparator.comparing(FenQiLeRepayPlan::getLoanTerm)).collect(Collectors.toList());

        response.setStatus(0);
        response.setRepayPlans(repayPlans);
        return response;
    }

    /**
     * 还款试算接口
     *
     * @param request 请求参数
     * @return 返回数据
     */
    public FenQiLeRepayTrialResponse trail(FenQiLeRepayTrialRequest request) {
        FenQiLeRepayTrialResponse trialResponse = new FenQiLeRepayTrialResponse();
        logger.info("分期乐还款试算请求参数:{}", JsonUtil.toJsonString(request));
        checkParameters(request);
        // 借据号
        String capitalLoanNo = request.getCapitalLoanNo();
        Loan loan = loanRepository.findById(capitalLoanNo).orElseThrow(() -> new FenQiLeBizException(FenQiLeResultCode.LOAN_NOT_EXIST));
        // 还款时间段校验
        repayTrailCheck(loan);
        RepayPurpose repayPurpose = convertToRepayPurpose(request.getRepayType());
        int repayPeriod = request.getRepayTerm();
        // 如果是结清,传入最小未还的期次
        if (RepayPurpose.CLEAR == repayPurpose) {
            if (CollectionUtils.isEmpty(repayPlanRepository.findByLoanIdAndCustRepayStateOrderByPeriodAsc(capitalLoanNo, RepayState.NORMAL))) {
                // throw new FenQiLeBizException(FenQiLeResultCode.REPAY_PLAN_NORMAL_NOT_EXIST);
                trialResponse.setCode(FenQiLeRepayTrialReturnCodeEnum.NO_SUCH_ORDER_FOUND.getCode());
                return trialResponse;
            }
        }
        Optional<CustomRepayRecord> successRepayRecord = customRepayRecordRepository.findByLoanIdAndPeriodAndRepayState(
            capitalLoanNo, repayPeriod, ProcessState.SUCCEED);
        if (successRepayRecord.isPresent()) {
            logger.error("分期乐还款试算失败，本期已还款成功,loanId:{},periods:{}", capitalLoanNo, repayPeriod);
            // throw new FenQiLeBizException(FenQiLeResultCode.REPAY_PLAN_ALREADY_SUCCESS);
            trialResponse.setCode(FenQiLeRepayTrialReturnCodeEnum.SETTLED.getCode());
            return trialResponse;
        }
        try {
            // 结算日期转换-分期乐接口传的结算日格式为yyyy-MM-dd需要转换为yyyyMMdd格式数据，因为试算接口用到的repayDate参数的值格式为yyyyMMdd
            request.setRepayDate(request.getRepayDate().replaceAll("-", ""));
            TrialResultVo resultVo = trialService.repayTrial(capitalLoanNo, repayPurpose, repayPeriod, request.getRepayDate());
            trialResponse = FenQiLeConvert.INSTANCE.toRepayTrailResponse(resultVo);
            trialResponse.setRepayPrincipal(trialResponse.getRepayPrincipal().setScale(2, RoundingMode.HALF_DOWN));
            trialResponse.setRepayFee(trialResponse.getRepayFee().setScale(2, RoundingMode.HALF_DOWN));
            trialResponse.setRepayMuclt(trialResponse.getRepayMuclt().setScale(2, RoundingMode.HALF_DOWN));
            trialResponse.setRepayTotal(trialResponse.getRepayTotal().setScale(2, RoundingMode.HALF_DOWN));
            logger.info("分期乐还款试算返回:{}", JsonUtil.toJsonString(trialResponse));
            trialResponse.setCode(FenQiLeRepayTrialReturnCodeEnum.SUCCESS.getCode());
            return trialResponse;
        } catch (BizException e) {
            logger.error("分期乐还款试算异常", e);
            // throw new FenQiLeBizException(e.getMessage(), FenQiLeResultCode.REPAY_TRIAL_ERROR);
            trialResponse.setCode(FenQiLeRepayTrialReturnCodeEnum.FAIL.getCode());
            trialResponse.setMsg(FenQiLeResultCode.REPAY_TRIAL_ERROR.getMsg());
            return trialResponse;
        } catch (Exception e) {
            logger.error("分期乐还款试算异常", e);
            // throw new FenQiLeBizException(FenQiLeResultCode.SYSTEM_ERROR);
            trialResponse.setCode(FenQiLeRepayTrialReturnCodeEnum.ABNORMAL.getCode());
            trialResponse.setMsg(FenQiLeResultCode.SYSTEM_ERROR.getMsg());
            return trialResponse;
        }
    }

    private static void checkParameters(FenQiLeRepayTrialRequest request) {
        // 借据号
        String capitalLoanNo = request.getCapitalLoanNo();
        // 还款类型
        String repayType = request.getRepayType();
        if (StringUtil.isBlank(capitalLoanNo)) {
            throw new FenQiLeBizException(FenQiLeResultCode.CAPITAL_LOAN_NO_CAN_NOT_BE_NULL);
        }
        if (StringUtil.isBlank(repayType)) {
            throw new FenQiLeBizException(FenQiLeResultCode.REPAY_PURPOSE_ERROR);
        } else {
            if (!"10".equals(repayType) && !"30".equals(repayType)) {
                throw new FenQiLeBizException(FenQiLeResultCode.REPAY_PURPOSE_ERROR_NOT_SUPPORTED);
            }
        }
    }

    private void repayTrailCheck(Loan loan) {
        if (loan.getLoanTime().toLocalDate().isEqual(LocalDate.now())) {
            throw new FenQiLeBizException(FenQiLeResultCode.REPAY_NOT_SUPPORTED_LOAN_DATE);
        }
    }

    private RepayPurpose convertToRepayPurpose(String repayType) {
        return switch (repayType) {
            case "10" -> RepayPurpose.CURRENT;
            case "30" -> RepayPurpose.CLEAR;
            default -> null;
        };
    }

    /**
     * 代扣还款申请
     *
     * @param request 请求参数
     * @return 返回数据
     */
    public FenQiLeRepayResponse repay(FenQiLeRepayRequest request) {
        FenQiLeRepayResponse response = new FenQiLeRepayResponse();
        checkParameters(request);
        // 映射数据及保存扣款申请记录信息
        FenQiLeRepayParamer repayParamer = mappingDataAndSaveRepayApplyRecord(request);
        // 借据号
        String loanId = repayParamer.getLoanGid();
        Loan loan = loanRepository.findById(loanId).orElseThrow(() -> new FenQiLeBizException(FenQiLeResultCode.LOAN_NOT_EXIST));
        String orderNo = loan.getOrderId();
        // 判断是否提前结清
        // List<Integer> repayList = new ArrayList<>(Collections.singletonList(repayParamer.getPeriod()));
        RepayPurpose repayPurpose = RepayPurpose.toFqlRepayType(repayParamer.getRepayType());
        int period = repayParamer.getPeriod();
        Optional<CustomRepayRecord> successRepayRecord = customRepayRecordRepository.
            findByLoanIdAndPeriodAndRepayState(loanId, period, ProcessState.SUCCEED);
        if (successRepayRecord.isPresent()) {
            logger.error("分期乐还款申请失败，本期已还款成功，orderNo:{},loanId:{},periods:{}", orderNo, loanId, period);
            throw new FenQiLeBizException(FenQiLeResultCode.REPAY_PLAN_ALREADY_SUCCESS);
        }
        // 查询还款卡信息
        UserBankCard bankCardList = userBankCardRepository.findById(loan.getRepayCardId()).orElse(null);
        // 如果是没有跟换银行卡，就直接发起还款
        if (ObjectUtils.isEmpty(repayParamer.getBindCardInfo())) {
            // 没有贷后换绑卡, 直接发起还款
            fenQiLeOnlineRepay(repayParamer);
            response.setStatus(1);
            return response;
        } else if (!ObjectUtils.isEmpty(bankCardList) && bankCardList.getCardNo().equals(repayParamer.getBindCardInfo().getCardNo())) {
            // 没有贷后换绑卡, 直接发起还款
            fenQiLeOnlineRepay(repayParamer);
            response.setStatus(1);
            return response;
        }
        // 存在贷后换绑卡
        String lockKey = FlowChannel.FQLQY001.name() + "_bind_apply_" + repayParamer.getBindCardInfo().getCardNo();
        Locker lock = lockService.getLock(lockKey);
        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (!locked) {
                logger.error("分期乐代扣申请，重复提交，orderNo:{}", orderNo);
                throw new FenQiLeBizException(FenQiLeResultCode.REPEAT_SUBMIT);
            }
            return exchangeBindApply(repayParamer, loan, orderNo);
        } catch (Exception e) {
            logger.error("分期乐还款异常: 存在换绑,资方绑卡申请处理失败", e);
            throw new FenQiLeBizException(FenQiLeResultCode.SYSTEM_ERROR);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 扣款申请数据校验
     *
     * @param request 请求数据
     */
    private static void checkParameters(FenQiLeRepayRequest request) {
        // 代扣请求流水号
        String withholdSerialNo = request.getWithholdSerialNo();
        if (StringUtil.isBlank(withholdSerialNo)) {
            throw new FenQiLeBizException(FenQiLeResultCode.WITHHOLD_SERIAL_NO_CAN_NOT_BE_NULL);
        }
        // 合作方代码
        String partnerCode = request.getPartnerCode();
        if (StringUtil.isBlank(partnerCode)) {
            throw new FenQiLeBizException(FenQiLeResultCode.PARTNER_CODE_CAN_NOT_BE_NULL);
        }
        // 代扣总金额
        BigDecimal withholdAmt = request.getWithholdAmt();
        if (null == withholdAmt) {
            throw new FenQiLeBizException(FenQiLeResultCode.WITHHOLD_AMT_CAN_NOT_BE_NULL);
        }
        // 代扣明细
        List<WithholdDetailInfo> withholdDetail = request.getWithholdDetail();
        if (CollectionUtils.isEmpty(withholdDetail)) {
            throw new FenQiLeBizException(FenQiLeResultCode.WITHHOLD_DETAIL_CAN_NOT_BE_NULL);
        }
    }

    /**
     * 扣款申请记录数据校验和保存还款申请记录以及组装数据
     */
    private FenQiLeRepayParamer mappingDataAndSaveRepayApplyRecord(FenQiLeRepayRequest request) {
        FenQiLeRepayParamer repayParamer = new FenQiLeRepayParamer();
        // 代扣请求流水号
        String withholdSerialNo = request.getWithholdSerialNo();
        repayParamer.setRepaymentGid(withholdSerialNo);
        // 放款的借据单号
        String loanId = request.getWithholdDetail().get(0).getCapitalLoanNo();
        repayParamer.setLoanGid(loanId);
        // 判断代扣总金额和代扣明细总金额是否一致
        // 代扣明细总金额
        BigDecimal rpyTotalAmt = request.getWithholdDetail().stream().map(WithholdDetailInfo::getRpyTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (request.getWithholdAmt().compareTo(rpyTotalAmt) != 0) {
            logger.error("分期乐还款申请，代扣总金额与代扣明细总金额不相等无法正常发起代扣申请，loanId:{},outRepayId:{},withholdAmt:{},rpyTotalAmt:{}",
                loanId, withholdSerialNo, request.getWithholdAmt(), rpyTotalAmt);
            throw new FenQiLeBizException(FenQiLeResultCode.WITHHOLD_AMT_RPYTOTAL_AMT_INCONSISTENT);
        }
        // 判断还款类型（0-待还、1-正常还款、2-部分提前还、 3-逾期还款 、4-全部提前还 、5-坏账代偿、 6-回购） 只支持1、3、4还款，其他类型不支持
        Integer rpyType = request.getWithholdDetail().get(0).getRpyType();
        if (rpyType != 1 && rpyType != 3 && rpyType != 4) {
            logger.error("分期乐还款申请，还款类型不支持，loanId:{},outRepayId:{},rpyType:{}", loanId, withholdSerialNo, rpyType);
            throw new FenQiLeBizException(FenQiLeResultCode.RPY_TYPE_NOT_SUPPORTED);
        }
        // 检查重复提交
        boolean existsRecord = fqlRepayApplyRecordRepository.existsByWithholdSerialNo(request.getWithholdSerialNo());
        if (existsRecord) {
            logger.error("分期乐还款申请，重复提交，loanId:{},outRepayId:{}", loanId, withholdSerialNo);
            throw new FenQiLeBizException(FenQiLeResultCode.REPEAT_SUBMIT);
        }
        // 保存扣款申请记录数据、绑卡信息、出账、分账、代扣明细及还款账单明细数据
        // 绑卡信息主键
        String userBankCardId = "";
        // 1、保存绑卡信息数据
        if (null != request.getBindCardInfo()) {
            FqlUserBankCard fqlUserBankCard = FenQiLeConvert.INSTANCE.toFqlUserBankCard(request.getBindCardInfo());
            fqlUserBankCard = fqlUserBankCardRepository.save(fqlUserBankCard);
            userBankCardId = fqlUserBankCard.getId();
            repayParamer.setBindCardInfo(request.getBindCardInfo());
        }
        // 2、保存扣款申请记录数据
        FqlRepayApplyRecord repayApplyRecord = FenQiLeConvert.INSTANCE.toFqlRepayApplyRecord(request);
        repayApplyRecord.setLoanId(loanId);
        repayApplyRecord.setUserBankCardId(userBankCardId);
        repayApplyRecord.setRepayChannel(FenQiLeRepayChannelEnum.WITHHOLDING.getCode());
        repayApplyRecord = fqlRepayApplyRecordRepository.save(repayApplyRecord);
        // 3、保存出账信息
        if (!CollectionUtils.isEmpty(request.getSepOutInfo())) {
            for (SepOutInfo sepOutInfo : request.getSepOutInfo()) {
                FqlSepOutInfo fqlSepOutInfo = FenQiLeConvert.INSTANCE.toFqlSepOutInfo(sepOutInfo);
                fqlSepOutInfo.setFqlRepayApplyRecordId(repayApplyRecord.getId());
                fqlSepOutInfoRepository.save(fqlSepOutInfo);
            }
        }
        // 4、保存分账信息数据
        if (!CollectionUtils.isEmpty(request.getSepInInfo())) {
            for (SepInInfo sepInInfo : request.getSepInInfo()) {
                FqlSepInInfo fqlSepInInfo = FenQiLeConvert.INSTANCE.toFqlSepInInfo(sepInInfo);
                fqlSepInInfo.setWithholdMerchants(sepInInfo.getWithholdMerchants().toString());
                fqlSepInInfo.setOrgType(sepInInfo.getOrgType().toString());
                fqlSepInInfo.setDetail(JsonUtil.toJsonString(sepInInfo.getDetail()));
                fqlSepInInfo.setType(sepInInfo.getType().toString());
                fqlSepInInfo.setFqlRepayApplyRecordId(repayApplyRecord.getId());
                fqlSepInInfoRepository.save(fqlSepInInfo);
            }
        }
        // 5、保存代扣明细信息
        WithholdDetailInfo withholdDetailInfo = request.getWithholdDetail().get(0);
        FqlWithholdDetail fqlWithholdDetail = FenQiLeConvert.INSTANCE.toFqlWithholdDetail(withholdDetailInfo);
        fqlWithholdDetail.setFqlRepayApplyRecordId(repayApplyRecord.getId());
        fqlWithholdDetail = fqlWithholdDetailRepository.save(fqlWithholdDetail);
        repayParamer.setRepayType(fqlWithholdDetail.getRpyType().toString());
        repayParamer.setRepayMode("ONLINE");
        repayParamer.setRepayDate(fqlWithholdDetail.getRpyDate());
        repayParamer.setRepayTotalAmount(fqlWithholdDetail.getRpyTotalAmt().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_DOWN));
        // 6、保存还款账单明细
        if (!CollectionUtils.isEmpty(withholdDetailInfo.getBillDetails())) {
            List<BillDetailsInfo> billDetailsInfos = withholdDetailInfo.getBillDetails().stream().sorted(Comparator.comparing(BillDetailsInfo::getRpyTerm)).toList();
            for (int i = 0; i < billDetailsInfos.size(); i++) {
                BillDetailsInfo billDetailsInfo = billDetailsInfos.get(i);
                if (i == 0) {
                    repayParamer.setPeriod(billDetailsInfo.getRpyTerm());
                }
                FqlBillDetails fqlBillDetails = FenQiLeConvert.INSTANCE.toFqlBillDetails(billDetailsInfo);
                fqlBillDetails.setFqlWithholdDetailId(fqlWithholdDetail.getId());
                fqlBillDetails = fqlBillDetailsRepository.save(fqlBillDetails);
                List<OtherInfo> otherInfos = billDetailsInfo.getOtherInfo();
                for (OtherInfo otherInfo : otherInfos) {
                    FqlBillDetailsOtherInfo fqlBillDetailsOtherInfo = new FqlBillDetailsOtherInfo();
                    fqlBillDetailsOtherInfo.setFee(otherInfo.getFee());
                    fqlBillDetailsOtherInfo.setType(otherInfo.getType());
                    fqlBillDetailsOtherInfo.setSubType(otherInfo.getSubType());
                    fqlBillDetailsOtherInfo.setInsureMode(otherInfo.getInsureMode());
                    fqlBillDetailsOtherInfo.setFqlBillDetailsId(fqlBillDetails.getId());
                    fqlBillDetailsOtherInfoRepository.save(fqlBillDetailsOtherInfo);
                }

            }
        }
        // 1、初始化外部代扣表数据
        OutWithholdFlow outWithholdFlow = new OutWithholdFlow();
        outWithholdFlow.setLoanId(loanId);
        outWithholdFlow.setPeriod(repayParamer.getPeriod());
        outWithholdFlow.setPayState(ProcessState.INIT);
        outWithholdFlow.setCommonWithholdType("flow-fql-capital");
        outWithholdFlow.setAgreementNo(request.getSignNum());
        outWithholdFlow.setRepayMode(RepayMode.ONLINE);
        outWithholdFlow = outWithholdFlowRepository.save(outWithholdFlow);
        // 2、初始化外部代扣出账信息
        OutWithholdOutInfo outWithholdOutInfo = new OutWithholdOutInfo();
        if (!CollectionUtils.isEmpty(request.getSepOutInfo())) {
            for (SepOutInfo sepOutInfo : request.getSepOutInfo()) {
                // 用户账户出账
                if ("1".equals(sepOutInfo.getType())) {
                    outWithholdOutInfo.setUserAmount(sepOutInfo.getAmt());
                    outWithholdOutInfo.setUserAccount(sepOutInfo.getAccount());
                }
                // 补差账户出账(即营销金额)
                if ("2".equals(sepOutInfo.getType())) {
                    outWithholdOutInfo.setDiffAmount(sepOutInfo.getAmt());
                    outWithholdOutInfo.setDiffAccount(sepOutInfo.getAccount());
                }
            }
        }
        outWithholdOutInfo.setOutWithholdFlowId(outWithholdFlow.getId());
        outWithholdOutInfoRepository.save(outWithholdOutInfo);
        // 3、初始化外部代扣分账信息表
        OutWithholdShareInfo outWithholdShareInfo = new OutWithholdShareInfo();
        outWithholdShareInfo.setOutWithholdFlowId(outWithholdFlow.getId());
        outWithholdShareInfo.setMerchantNo(request.getSubMerchantId());
        outWithholdShareInfoRepository.save(outWithholdShareInfo);
        logger.info("分期乐还款申请组装之后的数据:{}", JsonUtil.toJsonString(repayParamer));
        return repayParamer;
    }

    /**
     * 判断是否是结清操作，如果是结清则取最小期数
     *
     * @param repayList    期数list
     * @param repayPurpose 还款类型
     * @return 返回数据
     */
    private int getMinPeriod(List<Integer> repayList, RepayPurpose repayPurpose) {
        // 当期还款只传1期
        int repayPeriod = repayList.get(0);
        if (RepayPurpose.CLEAR.equals(repayPurpose)) {
            // 取最早的一期
            repayList = repayList.stream().sorted().toList();
            repayPeriod = repayList.get(0);
        }
        return repayPeriod;
    }

    /**
     * 判断是线上还是线下还款，然后调用还款公共方法
     *
     * @param request 请求参数
     * @return 返回数据
     */
    private String fenQiLeOnlineRepay(FenQiLeRepayParamer request) {
        String loanId = request.getLoanGid();
        Loan loan = loanRepository.findById(loanId).orElseThrow(() -> new FenQiLeBizException(FenQiLeResultCode.LOAN_NOT_EXIST));
        // 当期还款只传1期
        int repayPeriod = request.getPeriod();
        String repayNo = "";
        if (RepayMode.ONLINE.name().equals(request.getRepayMode())) {
            // 判断是否提前结清
            RepayPurpose repayPurpose = RepayPurpose.toFqlRepayType(request.getRepayType());
            OnlineRepayApplyRequest repayApplyRequest = FenQiLeConvert.INSTANCE.toOnlineApplyRequest(loanId, repayPeriod, repayPurpose);
            repayApplyRequest.setOtuRepayNo(request.getRepaymentGid());
            repayApplyRequest.setConsultationFeeWaiver(BigDecimal.ZERO);
            repayApplyRequest.setPenaltyInterestWaiver(BigDecimal.ZERO);
            repayApplyRequest.setRepayTotalAmount(request.getRepayTotalAmount());
            repayService.online(repayApplyRequest);
            logger.info("分期乐 还款响应,loan:{}, customRepayRecord id:{}", loanId, repayNo);
        } else {
            // 结算日期转换-分期乐接口传的结算日格式为yyyy-MM-dd需要转换为yyyyMMdd格式数据，因为试算接口用到的repayDate参数的值格式为yyyyMMdd
            request.setRepayDate(request.getRepayDate().replaceAll("-", ""));
            // 判断是否提前结清
            RepayPurpose repayPurpose = RepayPurpose.toFqlRepayNotifyType(request.getRepayType());
            RepayApplyDto repayApplyReq = new RepayApplyDto();
            repayApplyReq.setAmount(request.getRepayTotalAmount());
            repayApplyReq.setOrderId(loan.getOrderId());
            repayApplyReq.setRepayPurpose(com.jinghang.ppd.api.enums.RepayPurpose.valueOf(repayPurpose.toString()));
            repayApplyReq.setPeriod(repayPeriod);
            OfflineRepayApplyRequest offlineRepayApply = ManageConvert.INSTANCE.toRepayApplyRequest(repayApplyReq);
            offlineRepayApply.setWriteOffType(WriteOffTypeEnum.DIRECT);
            offlineRepayApply.setOuterRepayNo(request.getRepaymentGid());
            offlineRepayApply.setConsultationFeeWaiver(BigDecimal.ZERO);
            offlineRepayApply.setPenaltyInterestWaiver(BigDecimal.ZERO);
            offlineRepayApply.setRepayDate(request.getRepayDate());
            repayService.offline(offlineRepayApply);
        }
        return repayNo;
    }

    /**
     * 换绑卡操作方法
     *
     * @param request 请求参数
     * @param loan    借据信息
     * @param orderNo 订单号
     * @return 返回数据
     */
    private FenQiLeRepayResponse exchangeBindApply(FenQiLeRepayParamer request, Loan loan, String orderNo) {
        FenQiLeRepayResponse response = new FenQiLeRepayResponse();
        // 先将之前处理中的置为失败
        FqlRebindRecord rebindRecordCapital = fqlRebindRecordRepository.
            findByCreditIdAndStateAndBoundSide(orderNo, ProcessState.PROCESSING, BoundSide.CAPITAL);
        if (rebindRecordCapital != null) {
            rebindRecordCapital.setState(ProcessState.FAILED);
            fqlRebindRecordRepository.save(rebindRecordCapital);
        }
        BindCardRecord capitalBindResult = capitalBindApply(loan, orderNo, request.getBindCardInfo());
        // 已签约,直接发起还款
        if (ProcessState.SUCCEED == capitalBindResult.getState()) {
            // 绑卡状态变更
            FqlRebindRecord rebindRecord = fqlRebindRecordRepository.findByCreditIdAndStateAndBoundSide(
                orderNo, ProcessState.PROCESSING, BoundSide.CAPITAL);
            Loan loanCard = loanRepository.findById(loan.getId()).orElse(null);
            rebindRecord.setState(ProcessState.SUCCEED);
            rebindRecord.setBindCardRecordId(loanCard.getRepayCardId());
            fqlRebindRecordRepository.save(rebindRecord);
            // 还款
            String repayNo = fenQiLeOnlineRepay(request);
            response.setStatus(1);
            return response;
        }
        // 绑卡申请成功,后续调用还款验证码接口
        if (ProcessState.PROCESSING == capitalBindResult.getState()) {
            response.setStatus(1);
            return response;
        }
        logger.error("分期乐还款异常: 存在换绑,资方绑卡申请返回失败,reason:{}", capitalBindResult.getFailReason());
        response.setStatus(2);
        response.setMsg(capitalBindResult.getFailReason());
        return response;
    }

    /**
     * 资金方换绑卡
     *
     * @param loan         借款订单
     * @param orderNo      订单号
     * @param bindCardInfo 用户绑卡信息
     * @return 返回数据
     */
    private BindCardRecord capitalBindApply(Loan loan, String orderNo, BindCardInfo bindCardInfo) {
        Order order = orderRepository.findById(orderNo).orElse(null);
        ExchangeCardApplyReq exchangeCardApplyReq = new ExchangeCardApplyReq();
        exchangeCardApplyReq.setLoanId(loan.getId());
        exchangeCardApplyReq.setPhone(bindCardInfo.getPhoneNo());
        exchangeCardApplyReq.setCardNo(bindCardInfo.getCardNo());
        exchangeCardApplyReq.setBoundSide(BoundSide.CAPITAL);
        exchangeCardApplyReq.setCardName(bindCardInfo.getUserName());
        exchangeCardApplyReq.setAgreeNo(bindCardInfo.getIdentityId());
        exchangeCardApplyReq.setIdNo(bindCardInfo.getIdNo());
        CardBin cardBin = platformCardService.queryCardBin(bindCardInfo.getCardNo());
        if (cardBin == null) {
            throw new BizException(ResultCode.CARD_NOT_SUPPORT);
        }
        String bankAbbr = cardBin.getBankAbbr();
        exchangeCardApplyReq.setBankCode(bankAbbr);
        exchangeCardApplyReq.setBankName(cardBin.getShortName());
        // 资方换绑卡
        BindCardRecord bindCardRecord = capitalCardService.bindExchangeApply(loan, exchangeCardApplyReq);
        FqlRebindRecord rebindRecord = buildFqlRebindRecord(loan, bindCardRecord, orderNo);
        fqlRebindRecordRepository.save(rebindRecord);
        return bindCardRecord;
    }

    private static FqlRebindRecord buildFqlRebindRecord(Loan byOuterLoanId, BindCardRecord bindCardRecordCapitalRe, String orderNo) {
        FqlRebindRecord rebindRecord = new FqlRebindRecord();
        rebindRecord.setState(ProcessState.PROCESSING);
        rebindRecord.setCreditId(orderNo);
        rebindRecord.setUserId(byOuterLoanId.getUserId());
        rebindRecord.setLoanStage(LoanStage.REPAY.name());
        rebindRecord.setBindCardRecordId(bindCardRecordCapitalRe.getId());
        rebindRecord.setBoundSide(BoundSide.CAPITAL);
        return rebindRecord;
    }

    private UserOcr createUserOcr(PreOrder preOrder, FqlCreditApplyRecord applyRecord) {
        UserOcr userOcr = FenQiLeConvert.INSTANCE.toUserOcr(applyRecord);
        userOcr.setHeadOssBucket(ossBucket);
        userOcr.setNationOssBucket(ossBucket);
        //身份证有效期
        userOcr.setCertValidStart(LocalDate.parse(applyRecord.getIdCardValidDate(), DateTimeFormatter.ofPattern("yyyyMMdd")));
        userOcr.setCertValidEnd(LocalDate.parse(applyRecord.getIdCardExpireDate(), DateTimeFormatter.ofPattern("yyyyMMdd")));

        return userOcr;
    }

    private UserFace createUserFace(PreOrder preOrder, FqlCreditApplyRecord applyRecord) {
        UserFace userFace = new UserFace();
        userFace.setFaceChannel("Face++");
        userFace.setFaceTime(LocalDateTime.now());
        userFace.setFaceScore(new BigDecimal("95"));
        userFace.setFacialSupplier("Face++");

        return userFace;
    }

    private UserDevice createUserDevice(FqlCreditApplyRecord applyRecord) {
//        String deviceInfo = applyRecord.getDeviceInfo();
//        JSONObject jsonObject = JSONObject.parseObject(deviceInfo);
        UserDevice userDevice = new UserDevice();
//        userDevice.setOsType(jsonObject.getString("osType"));
//        userDevice.setOsVersion(jsonObject.getString("systemVersion"));
//        userDevice.setGps(applyRecord.getLongitude() + "," + applyRecord.getLatitude());
//        userDevice.setIp(jsonObject.getString("userIP"));
//        userDevice.setDeviceId(jsonObject.getString("deviceId"));
        return userDevice;
    }

    private List<UserContactInfo> createUserContactInfos(FqlCreditApplyRecord applyRecord) {
        List<UserContactInfo> contractInfos = new ArrayList<>();
        UserContactInfo contractInfo = new UserContactInfo();
        contractInfo.setName(applyRecord.getContactName());
        contractInfo.setPhone(applyRecord.getContactMobile());
        contractInfo.setRelation(FenQiLeConvert.INSTANCE.toFqlRelationsEnum(applyRecord.getContactRel()));
        return contractInfos;
    }

//    private void checkFtpFileReady(FenQiLeCreditApplyRequest request) {
//
//        try {
//            sftpUtils.fileExists(getFtpUri(request.getCreditApplyTime(), request.getCreditApplyId(), request.getIdentiNo()));
//        } catch (Exception e) {
//            logger.error("与检查影像信息不存在", e);
//            if (e.getMessage().contains("No such file")) {
//                throw new FenQiLeBizException("检查证件信息不完整,请先上传证件信息");
//            }
//            throw new FenQiLeBizException("检查证件信息不完整");
//        }
//    }

    /**
     * 代扣结果查询
     *
     * @param request 请求参数
     * @return 返回结果
     */
    public FenQiLeRepayResultResponse repayQuery(FenQiLeRepayResultRequest request) {
        checkParameters(request);
        FenQiLeRepayResultResponse response = new FenQiLeRepayResultResponse();
        // 查询还款申请记录信息数据
        FqlRepayApplyRecord fqlRepayApplyRecord = fqlRepayApplyRecordRepository.
            findByWithholdSerialNoAndPartnerCode(request.getWithholdSerialNo(), request.getPartnerCode());
        if (null == fqlRepayApplyRecord) {
            response.setStatus(4);
            response.setMsg("代扣申请订单不存在");
            return response;
        }
        // 查询对客还款记录
        CustomRepayRecord customRepayRecord = customRepayRecordRepository.findByOuterRepayNo(request.getWithholdSerialNo());
        Optional.ofNullable(customRepayRecord).orElseThrow(() -> new CommonApiBizException(CommonApiResultCode.QUERY_ERROR_REPAY));
        // 查询借据信息数据
        Optional<Loan> oLoan = loanRepository.findById(customRepayRecord.getLoanId());
        if (oLoan.isEmpty()) {
            throw new CommonApiBizException(CommonApiResultCode.QUERY_ERROR_REPAY);
        }
        // 查询订单信息数据
        Order order = orderService.findById(oLoan.get().getOrderId());
        Optional.ofNullable(order).orElseThrow(() -> new CommonApiBizException(CommonApiResultCode.QUERY_ERROR_REPAY));
        // 资金方扣款交易流水号
        response.setOrderNum(customRepayRecord.getId());
        // 查询代扣明细信息
        FqlWithholdDetail fqlWithholdDetail = fqlWithholdDetailRepository.findByFqlRepayApplyRecordId(fqlRepayApplyRecord.getId()).orElseThrow();
        List<WithholdDetailResponse> detailResponses = new ArrayList<>();
        WithholdDetailResponse detailResponse = new WithholdDetailResponse();
        detailResponse.setAssetId(fqlWithholdDetail.getAssetId());
        detailResponse.setCapitalLoanNo(fqlWithholdDetail.getCapitalLoanNo());
        detailResponse.setRpyTotalAmt(fqlWithholdDetail.getRpyTotalAmt().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_DOWN));
        detailResponse.setRpyType(fqlWithholdDetail.getRpyType());
        detailResponse.setRpyDate(fqlWithholdDetail.getRpyDate());
        // 查询还款账单信息数据
        List<FqlBillDetails> fqlBillDetailsList = fqlBillDetailsRepository.findByFqlWithholdDetailId(fqlWithholdDetail.getId());
        List<RpyDetailsResponse> rpyDetailsResponseList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(fqlBillDetailsList)) {
            RpyDetailsResponse rpyDetailsResponse = null;
            for (FqlBillDetails fqlBillDetails : fqlBillDetailsList) {
                rpyDetailsResponse = new RpyDetailsResponse();
                rpyDetailsResponse.setRpyAmt(fqlBillDetails.getRpyAmt().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_DOWN));
                rpyDetailsResponse.setRpyPrincipal(fqlBillDetails.getRpyPrincipal().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_DOWN));
                rpyDetailsResponse.setRpyFeeAmt(fqlBillDetails.getRpyFeeAmt().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_DOWN));
                rpyDetailsResponse.setRpyMuclt(fqlBillDetails.getRpyMuclt().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_DOWN));
                List<FqlBillDetailsOtherInfo> fqlBillDetailsOtherInfos = fqlBillDetailsOtherInfoRepository.findByFqlBillDetailsId(fqlBillDetails.getId());
                List<OtherInfo> otherInfos = fqlBillDetailsOtherInfos.stream().map(f -> {
                    OtherInfo otherInfo = new OtherInfo();
                    otherInfo.setFee(f.getFee());
                    otherInfo.setType(f.getType());
                    otherInfo.setSubType(f.getSubType());
                    otherInfo.setInsureMode(f.getInsureMode());
                    return otherInfo;
                }).collect(Collectors.toList());
                rpyDetailsResponse.setOtherInfo(otherInfos);
                rpyDetailsResponse.setRpyTerm(fqlBillDetails.getRpyTerm());
                rpyDetailsResponse.setRpyDate(customRepayRecord.getRepaidDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                rpyDetailsResponseList.add(rpyDetailsResponse);
            }
        }
        detailResponse.setRpyDetails(rpyDetailsResponseList);
        detailResponses.add(detailResponse);
        response.setWithholdDetail(detailResponses);
        // 还款结果
        if (ProcessState.SUCCEED == customRepayRecord.getRepayState()) {
            response.setStatus(1);
        } else if (ProcessState.FAILED == customRepayRecord.getRepayState()) {
            response.setStatus(2);
            response.setMsg(customRepayRecord.getFailReason());
        } else {
            response.setStatus(3);
        }
        return response;
    }

    private static void checkParameters(FenQiLeRepayResultRequest request) {
        // 代扣请求流水号
        String withholdSerialNo = request.getWithholdSerialNo();
        // 合作方代码
        String partnerCode = request.getPartnerCode();
        if (StringUtil.isBlank(withholdSerialNo)) {
            throw new FenQiLeBizException(FenQiLeResultCode.WITHHOLD_SERIAL_NO_CAN_NOT_BE_NULL);
        }
        if (StringUtil.isBlank(partnerCode)) {
            throw new FenQiLeBizException(FenQiLeResultCode.PARTNER_CODE_CAN_NOT_BE_NULL);
        }
    }

    /**
     * 实还通知接口
     *
     * @param request 请求参数
     * @return 返回结果
     */
    public FenQiLeRealReturnNoticeResponse repayNotify(FenQiLeRealReturnNoticeRequest request) {
        FenQiLeRealReturnNoticeResponse response = new FenQiLeRealReturnNoticeResponse();
        // 数据校验
        checkParameters(request);
        // 映射数据及保存扣款申请记录信息
        FenQiLeRepayParamer repayParamer = mappingDataAndSaveData(request);
        // 借据号
        String loanId = repayParamer.getLoanGid();
        Loan loan = loanRepository.findById(loanId).orElseThrow(() -> new FenQiLeBizException(FenQiLeResultCode.LOAN_NOT_EXIST));
        String orderNo = loan.getOrderId();
        // 判断是否提前结清
        // List<Integer> repayList = new ArrayList<>(Collections.singletonList(repayParamer.getPeriod()));
        RepayPurpose repayPurpose = RepayPurpose.toFqlRepayNotifyType(repayParamer.getRepayType());
        int period = repayParamer.getPeriod();
        Optional<CustomRepayRecord> successRepayRecord = customRepayRecordRepository.findByLoanIdAndPeriodAndRepayState(loanId, period, ProcessState.SUCCEED);
        if (successRepayRecord.isPresent()) {
            logger.error("分期乐实还通知申请失败，本期已还款成功，orderNo:{},loanId:{},periods:{}", orderNo, loanId, period);
            throw new FenQiLeBizException(FenQiLeResultCode.REPAY_PLAN_ALREADY_SUCCESS);
        }
        // 查询还款卡信息
        UserBankCard bankCardList = userBankCardRepository.findById(loan.getRepayCardId()).orElse(null);
        // 没有贷后换绑卡, 直接发起还款
        fenQiLeOnlineRepay(repayParamer);
        response.setNotifyStatus(1);
        return response;
    }

    /**
     * 实还通知接口参数校验
     *
     * @param request q请求参数
     */
    private static void checkParameters(FenQiLeRealReturnNoticeRequest request) {
        // 合作方代码
        String partnerCode = request.getPartnerCode();
        if (StringUtil.isBlank(partnerCode)) {
            throw new FenQiLeBizException(FenQiLeResultCode.PARTNER_CODE_CAN_NOT_BE_NULL);
        }
        // 还款请求流水号
        String billId = request.getBillId();
        if (StringUtil.isBlank(billId)) {
            throw new FenQiLeBizException(FenQiLeResultCode.BILL_ID_CAN_NOT_BE_NULL);
        }
        // 贷款申请编号
        String applyId = request.getApplyId();
        if (StringUtil.isBlank(applyId)) {
            throw new FenQiLeBizException(FenQiLeResultCode.APPLY_ID_CAN_NOT_BE_NULL);
        }
        // 资金方放款编号/借据号
        String capitalLoanNo = request.getCapitalLoanNo();
        if (StringUtil.isBlank(capitalLoanNo)) {
            throw new FenQiLeBizException(FenQiLeResultCode.CAPITAL_LOAN_NO_CAN_NOT_BE_NULL);
        }
        // 还款总额,保留两位有效数字(单位:元)
        BigDecimal repayAmount = request.getRepayAmount();
        if (null == repayAmount) {
            throw new FenQiLeBizException(FenQiLeResultCode.REPAY_AMOUNT_CAN_NOT_BE_NULL);
        }
    }

    /**
     * 实还通知接口参数映射
     *
     * @param request 请求参数
     */
    private FenQiLeRepayParamer mappingDataAndSaveData(FenQiLeRealReturnNoticeRequest request) {
        FenQiLeRepayParamer repayParamer = new FenQiLeRepayParamer();
        // 还款请求流水号
        String billId = request.getBillId();
        repayParamer.setRepaymentGid(billId);
        // 放款的借据单号
        String loanId = request.getCapitalLoanNo();
        repayParamer.setLoanGid(loanId);
        // 判断还款类型（10-正常还款,30-提前结清,40-逾期还款,50-代偿） 只支持10、30、40还款，其他类型不支持
        Integer repayType = request.getRepayType();
        if (repayType != 10 && repayType != 30 && repayType != 40) {
            logger.error("分期乐实还通知申请，还款类型不支持，loanId:{},outRepayId:{},rpyType:{}", loanId, billId, repayType);
            throw new FenQiLeBizException(FenQiLeResultCode.RPY_TYPE_NOT_SUPPORTED);
        }
        // 检查重复提交
        boolean existsRecord = fqlRepayApplyRecordRepository.existsByWithholdSerialNo(billId);
        if (existsRecord) {
            logger.error("分期乐实还通知申请，重复提交，loanId:{},outRepayId:{}", loanId, billId);
            throw new FenQiLeBizException(FenQiLeResultCode.REPEAT_SUBMIT);
        }
        // 保存实还通知记录信息
        FqlRepayApplyRecord repayApplyRecord = new FqlRepayApplyRecord();
        repayApplyRecord.setPartnerCode(request.getPartnerCode());
        repayApplyRecord.setWithholdSerialNo(request.getBillId());
        repayApplyRecord.setLoanId(loanId);
        repayApplyRecord.setWithholdAmt(request.getRepayAmount());
        repayApplyRecord.setApplyId(request.getApplyId());
        repayApplyRecord.setRepayTerm(request.getRepayTerm());
        repayApplyRecord.setRepayType(request.getRepayType());
        repayApplyRecord.setRepayDate(request.getRepayDate());
        repayApplyRecord.setUserRepayDate(request.getUserRepayDate());
        repayApplyRecord.setRepayChannel(request.getRepayChannel());
        JSONObject object = new JSONObject();
        object.put("repayPrincipal", request.getRepayPrincipal() == null ? BigDecimal.ZERO : request.getRepayPrincipal());
        object.put("repayInterest", request.getRepayInterest() == null ? BigDecimal.ZERO : request.getRepayInterest());
        object.put("repayMuclt", request.getRepayMuclt() == null ? BigDecimal.ZERO : request.getRepayMuclt());
        object.put("repayGuarantee", request.getRepayGuarantee() == null ? BigDecimal.ZERO : request.getRepayGuarantee());
        object.put("repayCreditFee", request.getRepayCreditFee() == null ? BigDecimal.ZERO : request.getRepayCreditFee());
        object.put("repayGranteeConsultServiceFee", request.getRepayGranteeConsultServiceFee() == null ? BigDecimal.ZERO : request.getRepayGranteeConsultServiceFee());
        repayApplyRecord.setOtherParamer(JsonUtil.toJsonString(object));
        fqlRepayApplyRecordRepository.save(repayApplyRecord);

        //初始化外部代扣
        OutWithholdFlow outWithholdFlow = new OutWithholdFlow();
        outWithholdFlow.setLoanId(request.getCapitalLoanNo());
        outWithholdFlow.setPeriod(request.getRepayTerm());
        outWithholdFlow.setPayState(ProcessState.INIT);
        outWithholdFlow.setCommonWithholdType("flow-fql-capital");
        outWithholdFlow.setRepayMode(RepayMode.OFFLINE);
        outWithholdFlowRepository.save(outWithholdFlow);

        repayParamer.setPeriod(request.getRepayTerm());
        repayParamer.setRepayType(request.getRepayType().toString());
        repayParamer.setRepayMode("OFFLINE");
        repayParamer.setRepayDate(request.getRepayDate());
        repayParamer.setRepayTotalAmount(request.getRepayAmount());
        logger.info("分期乐实还接口申请组装之后的数据:{}", JsonUtil.toJsonString(repayParamer));
        return repayParamer;
    }

    /**
     * 实还通知查询接口
     *
     * @param request 请求参数
     * @return 返回结果
     */
    public FenQiLeRealReturnResultResponse repayNotifyQuery(FenQiLeRealReturnResultRequest request) {
        FenQiLeRealReturnResultResponse response = new FenQiLeRealReturnResultResponse();
        // 数据校验
        checkParameters(request);
        // 查询还款申请记录信息数据
        FqlRepayApplyRecord fqlRepayApplyRecord = fqlRepayApplyRecordRepository.
            findByWithholdSerialNoAndPartnerCode(request.getBillId(), request.getPartnerCode());
        if (null == fqlRepayApplyRecord) {
            response.setRepayStatus(FenQiLeRepayStatusEnum.DOES_NOT_EXIST.getCode());
            response.setMsg(FenQiLeRepayStatusEnum.DOES_NOT_EXIST.getDesc());
            return response;
        }
        response.setRepayAmount(fqlRepayApplyRecord.getWithholdAmt());
        if (StringUtil.isNotBlank(fqlRepayApplyRecord.getOtherParamer())) {
            JSONObject object = JSON.parseObject(fqlRepayApplyRecord.getOtherParamer(), JSONObject.class);
            if (null != object.getString("repayPrincipal")) {
                response.setRepayPrincipal(new BigDecimal(object.getString("repayPrincipal")));
            } else {
                response.setRepayPrincipal(BigDecimal.ZERO);
            }
            if (null != object.getString("repayInterest")) {
                response.setRepayInterest(new BigDecimal(object.getString("repayInterest")));
            } else {
                response.setRepayInterest(BigDecimal.ZERO);
            }
            if (null != object.getString("repayMuclt")) {
                response.setRepayMuclt(new BigDecimal(object.getString("repayMuclt")));
            } else {
                response.setRepayMuclt(BigDecimal.ZERO);
            }
            if (null != object.getString("repayGuarantee")) {
                response.setRepayGuarantee(new BigDecimal(object.getString("repayGuarantee")));
            } else {
                response.setRepayGuarantee(BigDecimal.ZERO);
            }
            if (null != object.getString("repayCreditFee")) {
                response.setRepayCreditFee(new BigDecimal(object.getString("repayCreditFee")));
            } else {
                response.setRepayCreditFee(BigDecimal.ZERO);
            }
            if (null != object.getString("repayGranteeConsultServiceFee")) {
                response.setRepayGranteeConsultServiceFee(new BigDecimal(object.getString("repayGranteeConsultServiceFee")));
            } else {
                response.setRepayGranteeConsultServiceFee(BigDecimal.ZERO);
            }
        }
        CustomRepayRecord customRepayRecord = customRepayRecordRepository.findByOuterRepayNo(request.getBillId());
        if (customRepayRecord == null) {
            response.setRepayStatus(FenQiLeRepayStatusEnum.DOES_NOT_EXIST.getCode());
            response.setMsg(FenQiLeRepayStatusEnum.DOES_NOT_EXIST.getDesc());
            return response;
        }
        // 还款结果
        ProcessState customRepayState = customRepayRecord.getRepayState();
        if (ProcessState.SUCCEED == customRepayState) {
            response.setRepayStatus(FenQiLeRepayStatusEnum.SUCCESS.getCode());
            response.setProcessTime(customRepayRecord.getRepaidDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            response.setMsg(FenQiLeRepayStatusEnum.SUCCESS.getDesc());
        } else if (ProcessState.FAILED == customRepayState) {
            response.setRepayStatus(FenQiLeRepayStatusEnum.FAIL.getCode());
            response.setMsg(customRepayRecord.getFailReason());
        } else if (ProcessState.PROCESSING == customRepayState) {
            response.setRepayStatus(FenQiLeRepayStatusEnum.PROCESSING.getCode());
            response.setMsg(FenQiLeRepayStatusEnum.PROCESSING.getDesc());
        } else {
            response.setRepayStatus(FenQiLeRepayStatusEnum.SUCCESS.getCode());
            response.setProcessTime(customRepayRecord.getRepaidDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            response.setMsg(FenQiLeRepayStatusEnum.SUCCESS.getDesc());
        }
        return response;
    }

    /**
     * 实还通知查询接口数据校验
     *
     * @param request 请求参数
     */
    private static void checkParameters(FenQiLeRealReturnResultRequest request) {
        // 合作方代码
        String partnerCode = request.getPartnerCode();
        if (StringUtil.isBlank(partnerCode)) {
            throw new FenQiLeBizException(FenQiLeResultCode.PARTNER_CODE_CAN_NOT_BE_NULL);
        }
        // 还款请求流水号/账单号
        String billId = request.getBillId();
        if (StringUtil.isBlank(billId)) {
            throw new FenQiLeBizException(FenQiLeResultCode.BILL_ID_CAN_NOT_BE_NULL);
        }
        // 资金方放款编号/借据号
        String capitalLoanNo = request.getCapitalLoanNo();
        if (StringUtil.isBlank(capitalLoanNo)) {
            throw new FenQiLeBizException(FenQiLeResultCode.CAPITAL_LOAN_NO_CAN_NOT_BE_NULL);
        }
    }

    public void FqlUserFileDownloadAndUpload(String applyRecordId) {
        try {
            logger.info("分期乐文件下载开始：applyRecordId{}", applyRecordId);
            FqlCreditApplyRecord applyRecord = fqlCreditApplyRecordRepository.findById(applyRecordId).orElseThrow();
            PreOrder preOrder = preOrderRepository.findByOrderNoAndFlowChannel(applyRecord.getCreditApplyId(), FlowChannel.FQLQY001).orElseThrow();
            String userId = preOrder.getOpenId();
            UserOcr userOcr = userOcrRepository.findByUserId(userId);
            UserFace userFace = userFaceRepository.findByUserId(userId);
            InputStream headImage;
            InputStream nationImage;
            InputStream faceImage;
            List<UpLoadInfo> upLoadInfos = parseUpLoadInfoJson(applyRecord.getUploadInfo());
            String[] ftpUri = getFtpUri(upLoadInfos);
            String idPositiveNation = ftpUri[0];
            String idNegativeHead = ftpUri[1];
            String livePhoto = ftpUri[2];
            logger.info("idPositiveNation:{},idNegativeHead:{},faceImage:{}", idPositiveNation, idNegativeHead, livePhoto);
            headImage = sftpUtils.fqlDownloadAsStream(idPositiveNation);
            nationImage = sftpUtils.fqlDownloadAsStream(idNegativeHead);
            faceImage = sftpUtils.fqlDownloadAsStream(livePhoto);
            idNegativeHead = StringUtils.substringBefore(idNegativeHead, "?");
            idPositiveNation = StringUtils.substringBefore(idPositiveNation, "?");
            livePhoto = StringUtils.substringBefore(livePhoto, "?");
            String headOssKey = uploadToOss(headImage, ossBucket, "head", applyRecord.getCreditApplyId(), StringUtils.substringAfterLast(idNegativeHead, "."));
            String nationOssKey = uploadToOss(nationImage, ossBucket, "nation", applyRecord.getCreditApplyId(), StringUtils.substringAfterLast(idPositiveNation, "."));
            String ossKey = uploadToOss(faceImage, ossBucket, "face", applyRecord.getCreditApplyId(), StringUtils.substringAfterLast(livePhoto, "."));
            userOcr.setHeadOssKey(headOssKey);
            userOcr.setNationOssKey(nationOssKey);
            userFace.setOssBucket(ossBucket);
            userFace.setOssKey(ossKey);
            userOcrRepository.save(userOcr);
            userFaceRepository.save(userFace);
            userFileService.saveIdCardFace(userId, userOcr.getHeadOssBucket(), userOcr.getHeadOssKey());
            userFileService.saveIdCardNation(userId, userOcr.getNationOssBucket(), userOcr.getNationOssKey());
            userFileService.saveFaceOcr(userId, userFace.getOssBucket(), userFace.getOssKey());

            //百维逻辑处理
            //上传百维影像文件
            //获取分期乐影像文件路径及名称
            String[] ftpImgPath = getFqlImgPath(upLoadInfos);
            String[] ftpFileName = getFqlImgFileName(upLoadInfos);
            //国徽
            ByteArrayOutputStream nationOutStream = sftpUtils.fqlDownloadImgAsByteStream(ftpUri[0]);
            sftpUtils.baiWeiUploadImgStreamToSftp(nationOutStream, ftpFileName[0], ftpImgPath[0]);
            //人脸
            ByteArrayOutputStream headOutStream = sftpUtils.fqlDownloadImgAsByteStream(ftpUri[1]);
            sftpUtils.baiWeiUploadImgStreamToSftp(headOutStream, ftpFileName[1], ftpImgPath[1]);
            //活像
            ByteArrayOutputStream faceOutStream = sftpUtils.fqlDownloadImgAsByteStream(ftpUri[2]);
            sftpUtils.baiWeiUploadImgStreamToSftp(faceOutStream, ftpFileName[2], ftpImgPath[2]);

            UserRiskRecordExternal riskRecord = userRiskRecordExternalRepository.findTopByUserIdOrderByCreatedTimeDesc(userId);
            //风控前签章
            agreementService.applyRegisterSignExternal(riskRecord.getId(),preOrder.getProjectCode());

            //百维风控
            //todo 使用 listenRiskApply 方法，内外风控使用同一个入口
            mqService.submitRiskApply(riskRecord.getId());

        } catch (Exception e) {
            logger.error("下载分期乐影像件异常", e);
            if (e.getMessage().contains("No such file")) {
                throw new FenQiLeBizException("未在ftp中拉取到影像件文件,请先上传影像件信息");
            }
            throw new FenQiLeBizException("下载分期乐影像件异常");
        }
    }

    private String[] getFtpUri(List<UpLoadInfo> upLoadInfos) {
        Map<String, List<UpLoadInfo>> fileTypeMap = upLoadInfos.stream()
            .collect(Collectors.groupingBy(
                UpLoadInfo::getFileType,
                Collectors.toList()
            ));
        UpLoadInfo idPositiveNationInfo = fileTypeMap.get("2").get(0);//国徽
        UpLoadInfo idNegativeHeadInfo = fileTypeMap.get("1").get(0);//人脸
        UpLoadInfo livePhotoInfo = fileTypeMap.get("3").get(0);//活体

        String idPositiveNation = idPositiveNationInfo.getFilePath() + "/" + idPositiveNationInfo.getFileName();
        String idNegativeHead = idNegativeHeadInfo.getFilePath() + "/" + idNegativeHeadInfo.getFileName();
        String livePhoto = livePhotoInfo.getFilePath() + "/" + livePhotoInfo.getFileName();
        return new String[]{idPositiveNation, idNegativeHead, livePhoto};
    }

    /**
     * 获取分期乐影像文件路径
     *
     * @param upLoadInfos
     * @return
     */
    private String[] getFqlImgPath(List<UpLoadInfo> upLoadInfos) {
        Map<String, List<UpLoadInfo>> fileTypeMap = upLoadInfos.stream()
            .collect(Collectors.groupingBy(
                UpLoadInfo::getFileType,
                Collectors.toList()
            ));
        UpLoadInfo idPositiveNationInfo = fileTypeMap.get("2").get(0);//国徽
        UpLoadInfo idNegativeHeadInfo = fileTypeMap.get("1").get(0);//人脸
        UpLoadInfo livePhotoInfo = fileTypeMap.get("3").get(0);//活体

        String idPositiveNation = idPositiveNationInfo.getFilePath();
        String idNegativeHead = idNegativeHeadInfo.getFilePath();
        String livePhoto = livePhotoInfo.getFilePath();
        return new String[]{idPositiveNation, idNegativeHead, livePhoto};
    }

    /**
     * 获取分期乐影像文件名称
     *
     * @param upLoadInfos
     * @return
     */
    private String[] getFqlImgFileName(List<UpLoadInfo> upLoadInfos) {
        Map<String, List<UpLoadInfo>> fileTypeMap = upLoadInfos.stream()
            .collect(Collectors.groupingBy(
                UpLoadInfo::getFileType,
                Collectors.toList()
            ));
        UpLoadInfo idPositiveNationInfo = fileTypeMap.get("2").get(0);//国徽
        UpLoadInfo idNegativeHeadInfo = fileTypeMap.get("1").get(0);//人脸
        UpLoadInfo livePhotoInfo = fileTypeMap.get("3").get(0);//活体

        String idPositiveNation = idPositiveNationInfo.getFileName();
        String idNegativeHead = idNegativeHeadInfo.getFileName();
        String livePhoto = livePhotoInfo.getFileName();
        return new String[]{idPositiveNation, idNegativeHead, livePhoto};
    }

    private String uploadToOss(InputStream inputStream, String bucket, String picType, String openId, String imageType) {
        String picKey = generateOssPicKey(openId, picType, imageType);
        try {
            fileService.uploadOss(bucket, picKey, inputStream);
        } catch (Exception e) {
            logger.error("lvxin info process pic error, openId: {}, fileType: {}", openId, picType);
            picKey = null;
        }
        return picKey;
    }

    private String generateOssPicKey(String openId, String prefix, String imageType) {
        String dayStr = com.jinghang.common.util.DateUtil.formatShort(new Date());
        // fixme  定制化为lvxin/info/ 需要确认
        return "FQL/info/" + dayStr + "/" + openId + "/" + prefix + "_"
            + UUID.randomUUID().toString().replaceAll("-", "") + "." + imageType;
    }
}
